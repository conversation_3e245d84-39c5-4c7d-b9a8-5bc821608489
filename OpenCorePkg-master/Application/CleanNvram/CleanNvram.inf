## @file
#  Clean several important nvram variables to recover from issues.
#
#  Copyright (c) 2018, vit9696. All rights reserved.<BR>
#
#  This program and the accompanying materials
#  are licensed and made available under the terms and conditions of the BSD License
#  which accompanies this distribution.  The full text of the license may be found at
#  http://opensource.org/licenses/bsd-license.php
#  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
#  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CleanNvram
  FILE_GUID                      = 09BD751B-774E-4F54-A339-D9D6D8274BD1
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = UefiMain

#
#  This flag specifies whether HII resource section is generated into PE image.
#
  UEFI_HII_RESOURCE_SECTION      = TRUE

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  CleanNvram.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  OpenCorePkg/OpenCorePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  OcBootManagementLib
  OcConsoleControlEntryModeGenericLib
  OcVariableLib
  UefiApplicationEntryPoint
  UefiBootServicesTableLib
