DataBase list
=============
Non-x86 models are excluded.

| Mac-model | DataBase |
|:----------|:---------|
||
MacPro1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP11.yaml)
MacPro2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP21.yaml)
MacPro3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP31.yaml)
MacPro4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP41.yaml)
MacPro5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP51.yaml)
MacPro6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP61.yaml)
MacPro7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacPro/MP71.yaml)
||
MacBook1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB11.yaml)
MacBook2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB21.yaml)
MacBook3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB31.yaml)
MacBook4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB41.yaml)
MacBook5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB51.yaml)
MacBook5,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB52.yaml)
MacBook6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB61.yaml)
MacBook7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB71.yaml)
MacBook8,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB81.yaml)
MacBook9,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB91.yaml)
MacBook10,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBook/MB101.yaml)
||
MacBookAir1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA11.yaml)
MacBookAir2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA21.yaml)
MacBookAir3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA31.yaml)
MacBookAir3,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA32.yaml)
MacBookAir4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA41.yaml)
MacBookAir4,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA42.yaml)
MacBookAir5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA51.yaml)
MacBookAir5,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA52.yaml)
MacBookAir6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA61.yaml)
MacBookAir6,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA62.yaml)
MacBookAir7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA71.yaml)
MacBookAir7,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA72.yaml)
MacBookAir8,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA81.yaml)
MacBookAir8,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA82.yaml)
MacBookAir9,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookAir/MBA91.yaml)
||
MacBookPro1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP11.yaml)
MacBookPro1,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP12.yaml)
MacBookPro2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP21.yaml)
MacBookPro2,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP22.yaml)
MacBookPro3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP31.yaml)
MacBookPro4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP41.yaml)
MacBookPro5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP51.yaml)
MacBookPro5,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP52.yaml)
MacBookPro5,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP53.yaml)
MacBookPro5,4 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP54.yaml)
MacBookPro5,5 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP55.yaml)
MacBookPro6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP61.yaml)
MacBookPro6,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP62.yaml)
MacBookPro7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP71.yaml)
MacBookPro8,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP81.yaml)
MacBookPro8,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP82.yaml)
MacBookPro8,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP83.yaml)
MacBookPro9,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP91.yaml)
MacBookPro9,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP91.yaml)
MacBookPro10,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP101.yaml)
MacBookPro10,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP102.yaml)
MacBookPro11,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP111.yaml)
MacBookPro11,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP112.yaml)
MacBookPro11,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP113.yaml)
MacBookPro11,4 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP114.yaml)
MacBookPro11,5 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP115.yaml)
MacBookPro12,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP121.yaml)
MacBookPro13,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP131.yaml)
MacBookPro13,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP132.yaml)
MacBookPro13,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP133.yaml)
MacBookPro14,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP141.yaml)
MacBookPro14,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP142.yaml)
MacBookPro14,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP143.yaml)
MacBookPro15,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP151.yaml)
MacBookPro15,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP152.yaml)
MacBookPro15,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP153.yaml)
MacBookPro15,4 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP154.yaml)
MacBookPro16,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP161.yaml)
MacBookPro16,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP162.yaml)
MacBookPro16,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP163.yaml)
MacBookPro16,4 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/MacBookPro/MBP164.yaml)
||
Macmini1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM11.yaml)
Macmini2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM21.yaml)
Macmini3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM31.yaml)
Macmini4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM41.yaml)
Macmini5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM51.yaml)
Macmini5,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM52.yaml)
Macmini5,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM53.yaml)
Macmini6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM61.yaml)
Macmini6,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM62.yaml)
Macmini7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM71.yaml)
Macmini8,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Macmini/MM81.yaml)
||
Xserve1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Xserve/XS11.yaml)
Xserve2,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Xserve/XS21.yaml)
Xserve3,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/Xserve/XS31.yaml)
||
iMacPro1,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMacPro/IMP11.yaml)
||
iMac4,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM41.yaml)
iMac4,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM42.yaml)
iMac5,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM51.yaml)
iMac5,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM52.yaml)
iMac6,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM61.yaml)
iMac7,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM71.yaml)
iMac8,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM81.yaml)
iMac9,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM91.yaml)
iMac10,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM101.yaml)
iMac11,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM111.yaml)
iMac11,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM112.yaml)
iMac11,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM113.yaml)
iMac12,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM121.yaml)
iMac12,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM122.yaml)
iMac13,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM131.yaml)
iMac13,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM132.yaml)
iMac13,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM133.yaml)
iMac14,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM141.yaml)
iMac14,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM142.yaml)
iMac14,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM143.yaml)
iMac14,4 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM144.yaml)
iMac15,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM151.yaml)
iMac16,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM161.yaml)
iMac16,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM162.yaml)
iMac17,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM171.yaml)
iMac18,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM181.yaml)
iMac18,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM182.yaml)
iMac18,3 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM183.yaml)
iMac19,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM191.yaml)
iMac19,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM192.yaml)
iMac20,1 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM201.yaml)
iMac20,2 | [Yes](https://github.com/acidanthera/OpenCorePkg/blob/master/AppleModels/DataBase/iMac/IM202.yaml)
