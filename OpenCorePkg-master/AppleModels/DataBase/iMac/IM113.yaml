BIOSVendor: "Apple Inc."
BIOSVersion: "99.0.0.0.0"
BIOSLegacyVersion: "IM112.88Z.F000.B00.1906132310"
BIOSReleaseDate: "06/13/2019"
SystemManufacturer: "Apple Inc."
SystemProductName: "iMac11,3"
SystemVersion: "1.0"
# Note, this one is used by macserial
SystemSerialNumber: "QP0312PBDNR"
SystemSKUNumber: ""
SystemFamily: "iMac"
BoardManufacturer: "Apple Inc."
# This and similar ones can be an array itself, like this:
# BoardProduct:
#  "Mac-00BE6ED71E35EB86"
#  "Mac-smthelse"
BoardProduct: "Mac-F2238BAE"
BoardVersion: "iMac11,3"
BoardAssetTag: ""
BoardType: 0xA
BoardLocationInChassis: "Part Component"
ChassisManufacturer: "Apple Inc."
ChassisType: 0xD
ChassisVersion: "Mac-F2238BAE"
ChassisAssetTag: ""
# FIXME: check
FirmwareFeatures: 0xE00DE137
# FIXME: check
FirmwareFeaturesMask: 0xFF1FFF3F
# FIXME: check
ExtendedFirmwareFeatures: 0x00000000E00DE137
# FIXME: check
ExtendedFirmwareFeaturesMask: 0x00000000FF1FFF3F
MemoryFormFactor: 0x9
# FIXME: check values (most should be correct?) and add missing, if any
ProcessorType:
  - 0x0601
  - 0x0901
  - 0x0601
  - 0x0701
# FIXME: Correct this one
ProcessorBusSpeed:
  - 12345
# FIXME: check
SmcGeneration: 1
# FIXME: check
SmcRevision:
  - 0x01
  - 0x59
  - 0x0f
  - 0x00
  - 0x00
  - 0x02
# FIXME: check
SmcBranch:
  - 0x6B
  - 0x37
  - 0x34
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcPlatform:
  - 0x6B
  - 0x37
  - 0x34
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcEPCI: 0x7D004
OEMStrings: |-
  Apple ROM Version
    Model:        IM112
    EFI Version:  99.0.0.0.0
    Date:         Thu Jun 13 23:10:16 2019
    Build Type:   Release
Specifications:
  CPUCodename:
    - "Lynnfield"
    - "Clarkdale"
    - "Clarkdale"
    - "Lynnfield"
  CPU:
    - "Intel Core i5-760 @ 2.80 GHz"
    - "Intel Core i3-550 @ 3.20 GHz"
    - "Intel Core i5-680 @ 3.60 GHz"
    - "Intel Core i7-870 @ 2.93 GHz"
  GPU:
    - "AMD ATI Radeon HD 5750"
    - "AMD ATI Radeon HD 5670"
  RAM:
    - "1333 MHz PC3-10600 DDR3 SDRAM"
  SystemReportName:
    - "iMac (27-inch, Mid 2010)"
  MarketingName:
    - "27\" iMac (Mid 2010)"
# Note, first model code is used by macserial
AppleModelCode:
  - "DNR"
  - "DB6"
  - "GQK"
  - "GQJ"
  - "GVX"
  - "GVW"
  - "H20"
  - "GWQ"
  - "GTP"
  - "GTQ"
  - "HSC"
  - "H9H"
  - "H9J"
  - "HPB"
  - "HPG"
  - "HC2"
  - "HC6"
  - "HC4"
  - "HS0"
  - "GZN"
  - "GZP"
  - "GZQ"
  - "HA3"
  - "HA8"
  - "HA9"
  - "HA4"
  - "HA7"
  - "HAJ"
  - "HAL"
  - "HAA"
  - "HAE"
  - "HAF"
  - "HAG"
  - "GX2"
  - "GX3"
  - "GXD"
  - "GXA"
  - "GXB"
  - "GXC"
  - "GXU"
  - "GYG"
  - "DNP"
  - "DB5"
  - "H21"
  - "H22"
  - "H8H"
  - "HJ7"
  - "HJQ"
  - "HJR"
  - "HJY"
  - "HJX"
  - "HJA"
  - "HJC"
  - "HJH"
  - "HJJ"
  - "GRQ"
  - "H8P"
  - "H0B"
# Note, first board code is used by macserial
# FIXME: check
AppleBoardCode:
  - "000"
# Note, first year is used by macserial
AppleModelYear:
  - 2010
  - 2011
MinimumOSVersion: "10.6.3"
MaximumOSVersion: "10.13.6"
