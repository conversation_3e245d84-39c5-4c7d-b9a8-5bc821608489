BIOSVendor: "Apple Inc."
BIOSVersion: "429.0.0.0.0"
BIOSLegacyVersion: "IM131.88Z.F000.B00.2203181115"
BIOSReleaseDate: "03/18/2022"
SystemManufacturer: "Apple Inc."
SystemProductName: "iMac13,2"
SystemVersion: "1.0"
# Note, this one is used by macserial
SystemSerialNumber: "C02JB041DNCW"
SystemSKUNumber: ""
SystemFamily: "iMac"
BoardManufacturer: "Apple Inc."
# This and similar ones can be an array itself, like this:
# BoardProduct:
#  "Mac-00BE6ED71E35EB86"
#  "Mac-smthelse"
BoardProduct: "Mac-FC02E91DDD3FA6A4"
BoardVersion: "iMac13,2"
BoardAssetTag: ""
BoardType: 0xA
BoardLocationInChassis: "Part Component"
ChassisManufacturer: "Apple Inc."
ChassisType: 0xD
ChassisVersion: "Mac-FC02E91DDD3FA6A4"
ChassisAssetTag: ""
PlatformFeature: 0x1
# FIXME: check
FirmwareFeatures: 0xE00DE137
# FIXME: check
FirmwareFeaturesMask: 0xFF1FFF3F
# FIXME: check
ExtendedFirmwareFeatures: 0x00000000E00DE137
# FIXME: check
ExtendedFirmwareFeaturesMask: 0x00000000FF1FFF3F
MemoryFormFactor: 0x9
# FIXME: check values (most should be correct?) and add missing, if any
ProcessorType:
  - 0x0604
  - 0x0604
  - 0x0604
  - 0x0704
# FIXME: Correct this one
ProcessorBusSpeed:
  - 12345
# FIXME: check
SmcGeneration: 2
# FIXME: check
SmcRevision:
  - 0x02
  - 0x11
  - 0x0f
  - 0x00
  - 0x00
  - 0x16
# FIXME: check
SmcBranch:
  - 0x64
  - 0x38
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcPlatform:
  - 0x64
  - 0x38
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcEPCI: 0x79006
OEMStrings: |-
  Apple ROM Version
    Model:        IM131
    EFI Version:  429.0.0.0.0
    Built by:     root@xapp620
    Date:         Fri Mar 18 11:15:39 PDT 2022
    Revision:     429 (B&I)
    ROM Version:  F000_B00
    Build Type:   Official Build, Release
    Compiler:     Apple clang version 12.0.0 (clang-1200.0.30.3)
Specifications:
  CPUCodename:
    - "Ivy Bridge"
    - "Ivy Bridge"
    - "Ivy Bridge"
    - "Ivy Bridge"
  CPU:
    - "Intel Core i5-3470S @ 2.90 GHz"
    - "Intel Core i5-3475S @ 2.90 GHz"
    - "Intel Core i5-3470 @ 3.20 GHz"
    - "Intel Core i7-3770 @ 3.40 GHz"
  GPU:
    - "NVIDIA GeForce GTX 660M"
    - "NVIDIA GeForce GTX 675MX"
    - "NVIDIA GeForce GTX 680MX"
  RAM:
    - "1600 MHz DDR3L SDRAM"
  SystemReportName:
    - "iMac (27-inch, Late 2012)"
  MarketingName:
    - "27\" iMac (Late 2012)"
# Note, first model code is used by macserial
AppleModelCode:
  - "DNCW"
  - "DNCV"
  - "DNMN"
  - "DNMP"
  - "F29N"
  - "F8QP"
  - "F8QQ"
  - "FD5T"
  - "FD5V"
  - "FFM8"
  - "FFM9"
  - "FFMC"
  - "FFMD"
  - "FFMF"
  - "FFMG"
  - "FFMJ"
  - "FFMK"
  - "FFML"
  - "FFMM"
  - "FFMN"
  - "FFW0"
  - "FFW1"
  - "FG47"
  - "FGMW"
  - "FGMY"
  - "FGRP"
  - "FL8M"
  - "FM8P"
  - "FM8Q"
  - "FMLH"
  - "FP13"
  - "FP62"
  - "FTQ5"
  - "FTQ4"
# Note, first board code is used by macserial
AppleBoardCode:
  - "F2FR"
  - "F653"
  - "F49P"
  - "F651"
  - "F49R"
  - "F652"
  - "DYW3"
  - "F64V"
  - "F0V5"
  - "F64W"
# Note, first year is used by macserial
AppleModelYear:
  - 2012
  - 2013
MinimumOSVersion: "10.8.2"
MaximumOSVersion: "10.15.7"
