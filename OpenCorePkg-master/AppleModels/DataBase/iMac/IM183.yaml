BIOSVendor: "Apple Inc."
BIOSVersion: "529.140.2.0.0"
BIOSLegacyVersion: "IM183.88Z.F000.B00.2406231057"
BIOSReleaseDate: "06/23/2024"
SystemManufacturer: "Apple Inc."
SystemProductName: "iMac18,3"
SystemVersion: "1.0"
# Note, this one is used by macserial
SystemSerialNumber: "C02TDHACJ1GJ"
SystemSKUNumber: ""
SystemFamily: "iMac"
BoardManufacturer: "Apple Inc."
# This and similar ones can be an array itself, like this:
# BoardProduct:
#  "Mac-BE088AF8C5EB4FA2"
#  "Mac-smthelse"
BoardProduct: "Mac-BE088AF8C5EB4FA2"
BoardVersion: "iMac18,3"
BoardRevision: 0x1F
BoardAssetTag: ""
BoardType: 0xA
BoardLocationInChassis: ""
ChassisManufacturer: "Apple Inc."
ChassisType: 0xD
ChassisVersion: "Mac-BE088AF8C5EB4FA2"
ChassisAssetTag: ""
PlatformFeature: 0x0
FirmwareFeatures: 0xFD0FF576
FirmwareFeaturesMask: 0xFF1FFF3F
ExtendedFirmwareFeatures: 0x00000008FD0FF576
ExtendedFirmwareFeaturesMask: 0x00000008FF1FFF3F
MemoryFormFactor: 0x9
ProcessorType:
  - 0x0605
  - 0x0705
# FIXME: Correct this one
ProcessorBusSpeed:
  - 12345
  - 12345
SmcGeneration: 2
SmcRevision:
  - 0x02
  - 0x41
  - 0x0f
  - 0x00
  - 0x00
  - 0x02
SmcBranch:
  - 0x6a
  - 0x31
  - 0x33
  - 0x33
  - 0x5f
  - 0x34
  - 0x5f
  - 0x35
SmcPlatform:
  - 0x6a
  - 0x31
  - 0x33
  - 0x35
  - 0x00
  - 0x00
  - 0x00
  - 0x00
SmcEPCI: 0xF07009
OEMStrings: |-
  Apple ROM Version
    BIOS ID:      IM183.88Z.F000.B00.2406231057
    Model:        IM183
    EFI Version:  529.140.2.0.0
    Built by:     root@mthq5
    Date:         Sun Jun 23 10:57:31 2024
    Revision:     529.140.2 (B&I)
    ROM Version:  F000_B00
    Build Type:   Official Build, RELEASE
    Compiler:     clang-1500.3.9.1
    UUID:         67B145F8-6797-3AF5-801A-9C1AC8140C24
    UUID:         48EE4E27-82CE-3AE7-ACB6-916AD379D1EC
Specifications:
  CPUCodename:
    - "Kaby Lake"
    - "Kaby Lake"
    - "Kaby Lake"
    - "Kaby Lake"
  CPU:
    - "Intel Core i5-7600K @ 3.80 GHz"
    - "Intel Core i5-7600 @ 3.50 GHz"
    - "Intel Core i5-7500 @ 3.40 GHz"
    - "Intel Core i7-7700K @ 4.20 GHz"
  GPU:
    - "Radeon Pro 570"
    - "Radeon Pro 575"
    - "Radeon Pro 580"
  RAM:
    - "8GB 2400 MHz DDR4 SDRAM"
  SystemReportName:
    - "iMac (Retina 5K, 27-inch, 2017)"
  MarketingName:
    - "27\" iMac (Mid 2017)"
# Note, first model code is used by macserial
AppleModelCode:
  - "J1GJ"
  - "J1GQ"
  - "J1GG"
  - "J9XC"
  - "J9X7"
  - "J9X6"
  - "J9X9"
  - "JNGD"
  - "JNGF"
  - "JCCR"
  - "JCCT"
  - "JT72"
  - "J9X8"
  - "JM3R"
  - "JM3V"
  - "J1GR"
  - "J1GP"
  - "J1GV"
  - "J1GT"
  - "J1GK"
  - "J1GH"
  - "J1GN"
  - "J1GL"
  - "J1GM"
  - "JCND"
  - "JC5L"
  - "JX8H"
  - "J2GJ"
  - "J609"
  - "J60C"
# Note, first board code is used by macserial
AppleBoardCode:
  - "J0PG"
  - "J0PH"
  - "J0PJ"
  - "J0PC"
  - "J0PD"
  - "J0PF"
  - "HX77"
  - "HX78"
  - "HX79"
  - "HX7C"
  - "HX7D"
  - "HX7F"
  - "HNL6"
  - "HNL7"
  - "HNL8"
# Note, first year is used by macserial
AppleModelYear:
  - 2017
  - 2018
  - 2019
MinimumOSVersion: "10.12.4"
MaximumOSVersion: "13.7.6"
