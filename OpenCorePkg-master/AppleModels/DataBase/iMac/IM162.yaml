BIOSVendor: "Apple Inc."
BIOSVersion: "489.0.0.0.0"
BIOSLegacyVersion: "IM162.88Z.F000.B00.2310070443"
BIOSReleaseDate: "10/07/2023"
SystemManufacturer: "Apple Inc."
SystemProductName: "iMac16,2"
SystemVersion: "1.0"
# Note, this one is used by macserial
SystemSerialNumber: "DGKQQ173GG7F"
SystemSKUNumber: ""
SystemFamily: "iMac"
BoardManufacturer: "Apple Inc."
# This and similar ones can be an array itself, like this:
# BoardProduct:
#  "Mac-00BE6ED71E35EB86"
#  "Mac-smthelse"
BoardProduct: "Mac-FFE5EF870D7BA81A"
BoardVersion: "iMac16,2"
BoardAssetTag: ""
BoardType: 0xA
# FIXME: check
BoardLocationInChassis: ""
ChassisManufacturer: "Apple Inc."
ChassisType: 0xD
ChassisVersion: "Mac-FFE5EF870D7BA81A"
ChassisAssetTag: ""
PlatformFeature: 0x3
# FIXME: check
FirmwareFeatures: 0xFC0FE137
# FIXME: check
FirmwareFeaturesMask: 0xFF1FFF3F
# FIXME: check
ExtendedFirmwareFeatures: 0x00000008FC0FE137
# FIXME: check
ExtendedFirmwareFeaturesMask: 0x00000008FF1FFF3F
MemoryFormFactor: 0x9
# FIXME: check values (most should be correct?) and add missing, if any
ProcessorType:
  - 0x0606
  - 0x0606
  - 0x0706
# FIXME: Correct this one
ProcessorBusSpeed:
  - 12345
# FIXME: check
SmcGeneration: 2
SmcRevision:
  - 0x02
  - 0x32
  - 0x0f
  - 0x00
  - 0x00
  - 0x21
# FIXME: check
SmcBranch:
  - 0x6A
  - 0x39
  - 0x34
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcPlatform:
  - 0x6A
  - 0x39
  - 0x34
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcEPCI: 0xF00008
OEMStrings: |-
  Apple ROM Version
    Model:        IM162
    EFI Version:  489.0.0.0.0
    Built by:     root@c2255
    Date:         Sat Oct  7 04:43:45 PDT 2023
    Revision:     489 (B&I)
    ROM Version:  F000_B00
    Build Type:   Official Build, Release
    Compiler:     clang-1500.1.0.2.2
Specifications:
  CPUCodename:
    - "Broadwell"
    - "Broadwell"
    - "Broadwell"
  CPU:
    - "Intel Core i5-5675R @ 3.10 GHz"
    - "Intel Core i5-5575R @ 2.80 GHz"
    - "Intel Core i7-5775R @ 3.30 GHz"
  GPU:
    - "Intel Iris Pro 6200"
  RAM:
    - "1867 MHz LPDDR3 SDRAM"
  SystemReportName:
    - "iMac (Retina 4K, 21.5-inch, Late 2015)"
  MarketingName:
    - "21.5\" iMac (Retina 4K Late 2015)"
# Note, first model code is used by macserial
AppleModelCode:
  - "GG7F"
  - "H15R"
  - "H3RJ"
  - "H8KY"
  - "H25N"
  - "GG7C"
  - "GG7H"
  - "H0P7"
  - "H8L0"
  - "H8L1"
  - "H8L2"
  - "H8L3"
  - "H0KF"
  - "H28H"
  - "GG78"
  - "HLWV"
  - "H1F3"
  - "H1F7"
  - "H1F5"
  - "H1F9"
  - "H1F8"
# Note, first board code is used by macserial
AppleBoardCode:
  - "GQRY"
  - "GQT0"
  - "GQT1"
  - "GPXJ"
  - "GPXK"
  - "GPXL"
  - "GQRW"
  - "GQRX"
  - "GXMM"
  - "GPXG"
  - "GPXH"
  - "GXMN"
  - "GQT4"
  - "GQT5"
  - "GQT6"
  - "GPXP"
  - "GPXQ"
  - "GPXR"
  - "GQT2"
  - "GQT3"
  - "GXMP"
  - "GPXM"
  - "GPXN"
  - "GXMQ"
# Note, first year is used by macserial
AppleModelYear:
  - 2015
  - 2016
  - 2017
MinimumOSVersion: "10.11"
MaximumOSVersion: "12.7.6"
