BIOSVendor: "Apple Inc."
BIOSVersion: "478.0.0.0.0"
BIOSLegacyVersion: "MBP112.88Z.F000.B00.2301131736"
BIOSReleaseDate: "01/13/2023"
SystemManufacturer: "Apple Inc."
SystemProductName: "MacBookPro11,2"
SystemVersion: "1.0"
# Note, this one is used by macserial
SystemSerialNumber: "C02LSHACG86R"
SystemSKUNumber: "System SKU#"
SystemFamily: "MacBook Pro"
BoardManufacturer: "Apple Inc."
# This and similar ones can be an array itself, like this:
# BoardProduct:
#  "Mac-00BE6ED71E35EB86"
#  "Mac-smthelse"
BoardProduct: "Mac-3CBD00234E554E41"
BoardVersion: "MacBookPro11,2"
BoardAssetTag: "Base Board Asset Tag#"
BoardType: 0xA
BoardLocationInChassis: "Part Component"
ChassisManufacturer: "Apple Inc."
ChassisType: 0xA
ChassisVersion: "Mac-3CBD00234E554E41"
ChassisAssetTag: ""
PlatformFeature: 0x2
FirmwareFeatures: 0xEB0FF577
FirmwareFeaturesMask: 0xFF1FFF7F
ExtendedFirmwareFeatures: 0x00000000EB0FF577
ExtendedFirmwareFeaturesMask: 0x00000000FF1FFF7F
MemoryFormFactor: 0xD
# FIXME: check values (most should be correct?) and add missing, if any
ProcessorType:
  - 0x0705
  - 0x0705
  - 0x0705
  - 0x0705
  - 0x0705
  - 0x0705
# FIXME: Correct this one
ProcessorBusSpeed:
  - 12345
# FIXME: check
SmcGeneration: 2
# FIXME: check
SmcRevision:
  - 0x02
  - 0x18
  - 0x0f
  - 0x00
  - 0x00
  - 0x15
# FIXME: check
SmcBranch:
  - 0x6A
  - 0x34
  - 0x35
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcPlatform:
  - 0x6A
  - 0x34
  - 0x35
  - 0x00
  - 0x00
  - 0x00
  - 0x00
  - 0x00
# FIXME: check
SmcEPCI: 0xF0B007
OEMStrings: |-
  Apple ROM Version
    Model:        MBP112
    EFI Version:  478.0.0.0.0
    Built by:     root@tmxfj
    Date:         Fri Jan 13 17:36:33 PST 2023
    Revision:     478 (B&I)
    ROM Version:  F000_B00
    Build Type:   Official Build, Release
    Compiler:     clang-1403.0.22.8
Specifications:
  CPUCodename:
    - "Crystal Well"
    - "Crystal Well"
    - "Crystal Well"
    - "Crystal Well"
    - "Crystal Well"
    - "Crystal Well"
  CPU:
    - "Intel Core i7-4770HQ @ 2.20 GHz"
    - "Intel Core i7-4850HQ @ 2.30 GHz"
    - "Intel Core i7-4870HQ @ 2.50 GHz"
    - "Intel Core i7-4960HQ @ 2.60 GHz"
    - "Intel Core i7-4750HQ @ 2.00 GHz"
    - "Intel Core i7-4980HQ @ 2.80 GHz"
  GPU:
    - "Intel Iris 5200 Pro graphics"
  RAM:
    - "1600 MHz DDR3L SDRAM"
  SystemReportName:
    - "MacBook Pro (Retina, 15-inch, Mid 2014)"
  MarketingName:
    - "15\" MacBook Pro with Retina display (Mid 2014)"
# Note, first model code is used by macserial
AppleModelCode:
  - "G86R"
  - "FD56"
  - "G3QC"
  - "G3QG"
  - "G3QN"
  - "G8J7"
  - "G8F4"
  - "G8L1"
  - "G974"
  - "G9L8"
  - "G9L9"
  - "G9L7"
  - "G96P"
  - "G96Q"
  - "G96K"
  - "G96N"
  - "G96L"
  - "G96M"
  - "G973"
  - "G9L6"
  - "GDPP"
  - "G9JN"
  - "G86Q"
  - "G86P"
  - "G9FT"
# Note, first board code is used by macserial
AppleBoardCode:
  - "FJQW"
  - "FJRP"
  - "FJRR"
  - "FJRH"
  - "FJRJ"
  - "FJRN"
  - "FJRC"
  - "FJRV"
  - "FJTQ"
  - "FJT1"
  - "FJTH"
  - "FJTL"
  - "FJRY"
  - "FJTC"
  - "FJTP"
  - "FJR7"
  - "FJR9"
  - "FJRM"
  - "G3M6"
  - "G3M8"
  - "G3M9"
  - "G3M4"
  - "G3M5"
  - "G3M7"
  - "G3MG"
  - "G3MJ"
  - "G3MK"
# Note, first year is used by macserial
AppleModelYear:
  - 2013
  - 2014
  - 2015
MinimumOSVersion: "10.9.4"
MaximumOSVersion: "11.7.10"
