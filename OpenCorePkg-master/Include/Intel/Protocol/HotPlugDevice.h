/** @file
  Copyright (c) 2004, Intel Corporation. All rights reserved.<BR>
  This program and the accompanying materials
  are licensed and made available under the terms and conditions of the BSD License
  which accompanies this distribution.  The full text of the license may be found at
  http://opensource.org/licenses/bsd-license.php

  THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
  WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
**/

#ifndef HOT_PLUG_DEVICE_H
#define HOT_PLUG_DEVICE_H

#define HOT_PLUG_DEVICE_GUID     \
  { 0x220AC432, 0x1D43, 0x49E5,  \
    { 0xA7, 0x4F, 0x4C, 0x9D, 0xA6, 0x7A, 0xD2, 0x3B } }

extern EFI_GUID  gEfiHotPlugDeviceGuid;

#endif // HOT_PLUG_DEVICE_H
