## @file
#
#  Component description file for Oc Variable Library.
#
#  Copyright (C) 2016, The HermitCrabs Lab. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = OcAcpiLib
  FILE_GUID      = 74A1EF2F-1374-4645-AFF4-3C3AD3697870
  MODULE_TYPE    = BASE
  VERSION_STRING = 1.0
  LIBRARY_CLASS  = OcAcpiLib|PEIM DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

# VALID_ARCHITECTURES = IA32 X64

[Packages]
  OpenCorePkg/OpenCorePkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib
  OcFileLib
  OcMemoryLib
  OcMiscLib
  PrintLib
  PciLib

[Guids]
  gEfiAcpi10TableGuid
  gEfiAcpi20TableGuid

[Sources]
  AcpiDump.c
  AcpiParser.c
  AcpiParser.h
  OcAcpiLib.c
