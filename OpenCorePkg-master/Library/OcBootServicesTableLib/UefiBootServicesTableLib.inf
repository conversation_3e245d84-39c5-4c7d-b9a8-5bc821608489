## @file
# UEFI Boot Services Table Library implementation with override support.
#
# Copyright (c) 2020, vit9696. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UefiBootServicesTableLib
  FILE_GUID                      = FEA6B92A-B21D-46DE-A74C-CDFB334CFBE3
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = UefiBootServicesTableLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER SMM_CORE


#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  UefiBootServicesTableLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Protocols]
  gEfiDevicePathProtocolGuid
  gEfiLoadedImageProtocolGuid

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  OcBootServicesTableLib
  # Libraries supporting local installation go here
  OcConsoleControlEntryModeLocalLib
  OcUnicodeCollationEngLocalLib
  OcHiiDatabaseLocalLib
