## @file
# UEFI Boot Services Table Library implementation with override support.
#
# Copyright (c) 2020, vit9696. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcBootServicesTableLib
  FILE_GUID                      = 64CCF528-1707-4DCB-84B7-EA02FEBB6A16
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcBootServicesTableLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER SMM_CORE

  CONSTRUCTOR                    = OcBootServicesTableLibConstructor
  DESTRUCTOR                     = OcBootServicesTableLibDestructor

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  OcBootServicesTableLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Protocols]
  gEfiDevicePathProtocolGuid

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DevicePathLib
