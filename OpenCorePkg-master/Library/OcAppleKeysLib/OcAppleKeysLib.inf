## @file
# OcAppleVerificationLib
#
# Copyright (c) 2017-2018, savvas
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleKeysLib
  FILE_GUID                      = 1CADD588-89D4-464C-92DC-FA0ABE350B16
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleKeysLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER SMM_CORE UEFI_APPLICATION UEFI_DRIVER


#
#  VALID_ARCHITECTURES           = X64
#

[Sources]
  OcAppleKeysLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[LibraryClasses]
  BaseLib

