## @file
#
#  Component description file for the library producing the Apple Firmware password protocol.
#
#  Copyright (C) 2020, vit9696. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAudioLib
  FILE_GUID                      = 7E6E8F20-FA64-4066-9A98-95AA0D83917C
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAudioLib|PEIM DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAudio.c
  OcAudioDump.c
  OcAudioGenBeep.c
  OcAudioLib.c
  OcAudioInternal.h
  OcAudioVoiceOver.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Guids]
  gAppleBootVariableGuid
  gEfiHdaIoDevicePathGuid

[Protocols]
  gAppleVOAudioProtocolGuid
  gAppleBeepGenProtocolGuid
  gEfiHdaControllerInfoProtocolGuid
  gEfiHdaCodecInfoProtocolGuid
  gAppleHighDefinitionAudioProtocolGuid
  gEfiAudioIoProtocolGuid
  gOcAudioProtocolGuid

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  BaseOverflowLib
  MemoryAllocationLib
  OcMiscLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
