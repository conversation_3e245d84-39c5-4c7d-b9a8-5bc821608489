## @file
# Copyright (C) 2019, Goldfish64. All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = OcAppleChunklistLib
  FILE_GUID      = D891DF81-0C83-47FF-ABAD-546050E1A07F
  MODULE_TYPE    = BASE
  VERSION_STRING = 1.0
  LIBRARY_CLASS  = OcAppleChunklistLib|PEIM DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  OcAppleRamDiskLib
  OcCryptoLib
  UefiLib

[Sources]
  OcAppleChunklistLib.c
