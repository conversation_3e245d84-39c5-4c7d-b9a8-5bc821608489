## @file
#
#  Component description file for the library producing the Apple Device property protocol.
#
#  Copyright (C) 2019, vit9696. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcBootManagementLib
  FILE_GUID                      = A28FEC6F-DD5C-4D8D-9351-50D9A2047634
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcBootManagementLib|PEIM DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  AppleHibernate.c
  ApplePanic.c
  AppleRecovery.c
  BootArguments.c
  BootAudio.c
  BootEntryInfo.c
  BootEntryManagement.c
  BootManagementInternal.h
  BootEntryProtocol.c
  BuiltinPicker.c
  DefaultEntryChoice.c
  DmgBootSupport.c
  HotKeySupport.c
  ImageLoader.c
  PolicyManagement.c
  OcBootManagementLib.c
  OcResetSystem.c

[Packages]
  OpenCorePkg/OpenCorePkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[Guids]
  gAppleApfsContainerInfoGuid                   ## SOMETIMES_CONSUMES
  gAppleApfsVolumeInfoGuid                      ## SOMETIMES_CONSUMES
  gAppleBlessedSystemFileInfoGuid               ## SOMETIMES_CONSUMES
  gAppleBlessedSystemFolderInfoGuid             ## SOMETIMES_CONSUMES
  gAppleBlessedOsxFolderInfoGuid                ## SOMETIMES_CONSUMES
  gEfiFileInfoGuid                              ## SOMETIMES_CONSUMES
  gEfiGlobalVariableGuid                        ## SOMETIMES_CONSUMES
  gEfiPartTypeSystemPartGuid                    ## SOMETIMES_CONSUMES
  gAppleApfsPartitionTypeGuid                   ## SOMETIMES_CONSUMES
  gAppleBootVariableGuid                        ## SOMETIMES_CONSUMES
  gAppleHfsPartitionTypeGuid                    ## SOMETIMES_CONSUMES
  gAppleHfsBootPartitionTypeGuid                ## SOMETIMES_CONSUMES
  gAppleLegacyLoadAppFileGuid                   ## SOMETIMES_CONSUMES
  gAppleVendorVariableGuid                      ## SOMETIMES_CONSUMES
  gEfiPartTypeUnusedGuid                        ## SOMETIMES_CONSUMES
  gOcVendorVariableGuid                         ## SOMETIMES_CONSUMES
  gAppleBootPickerFileGuid                      ## SOMETIMES_CONSUMES

[Protocols]
  gAppleBootPolicyProtocolGuid                  ## PRODUCES
  gAppleKeyMapAggregatorProtocolGuid            ## SOMETIMES_CONSUMES
  gEfiSimpleFileSystemProtocolGuid              ## SOMETIMES_CONSUMES
  gEfiLoadedImageProtocolGuid                   ## SOMETIMES_CONSUMES
  gEfiUsbIoProtocolGuid                         ## SOMETIMES_CONSUMES
  gOcFirmwareRuntimeProtocolGuid                ## SOMETIMES_CONSUMES
  gOcAudioProtocolGuid                          ## SOMETIMES_CONSUMES
  gAppleBeepGenProtocolGuid                     ## SOMETIMES_CONSUMES
  gOcBootEntryProtocolGuid                      ## CONSUMES
  gAppleFirmwareUserInterfaceProtocolGuid       ## SOMETIMES_CONSUMES

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  BaseOverflowLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  PrintLib
  UefiBootServicesTableLib
  UefiImageOnlyNonFvLib
  OcApfsLib
  OcAppleBootPolicyLib
  OcAppleChunklistLib
  OcAppleDiskImageLib
  OcAppleKeyMapLib
  OcAppleKeysLib
  OcAppleSecureBootLib
  OcConsoleLib
  OcCryptoLib
  OcDeviceMiscLib
  OcDevicePathLib
  OcFileLib
  OcFlexArrayLib
  OcMachoLib
  OcMiscLib
  OcPeCoffExtLib
  OcRtcLib
  OcTypingLib
  OcVariableLib
  OcXmlLib
  TimerLib
  FileHandleLib
  ResetSystemLib
