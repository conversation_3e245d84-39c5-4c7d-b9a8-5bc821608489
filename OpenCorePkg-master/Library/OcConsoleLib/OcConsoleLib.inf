## @file
# OcConsoleLib
#
# Copyright (c) 2019, vit9696
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcConsoleLib
  FILE_GUID                      = 509895E3-ABF8-4836-BB0C-919729A8C293
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcConsoleLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER SMM_CORE UEFI_APPLICATION UEFI_DRIVER

#
#  VALID_ARCHITECTURES           = X64
#

[Guids]
  gAppleVendorVariableGuid
  gAppleBootVariableGuid

[Protocols]
  gAppleFramebufferInfoProtocolGuid
  gAppleEg2InfoProtocolGuid
  gEfiConsoleControlProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiSimpleTextOutProtocolGuid
  gEfiUgaDrawProtocolGuid
  gOcForceResolutionProtocolGuid

[Sources]
  ConsoleControl.c
  ConsoleFont.c
  ConsoleFontLoader.c
  ConsoleGop.c
  ConsoleGopInternal.h
  Eg2Info.c
  FramebufferInfo.c
  GopInfoDump.c
  GopUtils.c
  GopPassThrough.c
  OcConsoleLib.c
  OcConsoleLibInternal.h
  ResolutionParsing.c
  TextOutputBuiltin.c
  TextOutputNull.c
  TextOutputSystem.c
  UgaPassThrough.c

[Packages]
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  OcBlitLib
  OcMemoryLib
  OcMiscLib
  OcStorageLib
  MtrrLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
