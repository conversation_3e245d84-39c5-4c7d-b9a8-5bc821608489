## @file
# Compiler intrinsic support library.
#
# Copyright (c) 2020, vit9696. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
##

[Defines]
  INF_VERSION                    = 1.5
  BASE_NAME                      = OcCompilerIntrinsicsLib
  FILE_GUID                      = AB87D1A9-F9EC-4B66-80EE-E88A15EAA254
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcCompilerIntrinsicsLib

[BuildOptions]
  # FIXME: MSVC compiler flags may need more thought.
  MSFT:*_*_*_CC_FLAGS = /GL- /Oi-

[Sources]
  OcCompilerIntrinsicsLib.c

[Sources.Ia32]
  MsvcMath32.c | MSFT

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec
