## @file
# OcAppleKernelLib
#
# Copyright (c) 2019, vit9696
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleKernelLib
  FILE_GUID                      = 9CFA01EA-9A9B-450C-B672-E108CA30DC3F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleKernelLib|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER SMM_CORE UEFI_APPLICATION UEFI_DRIVER


#
#  VALID_ARCHITECTURES           = X64
#

[Sources]
  KernelReader.c
  KextPatcher.c
  Link.c
  CommonPatches.c
  KernelCollection.c
  KernelVersion.c
  KxldState.c
  PrelinkedContext.c
  PrelinkedInternal.h
  PrelinkedKext.c
  Vtables.c
  CachelessContext.c
  MkextContext.c
  CpuidPatches.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  MemoryAllocationLib
  OcCompressionLib
  OcCpuLib
  OcFileLib
  OcMachoLib
  OcXmlLib

