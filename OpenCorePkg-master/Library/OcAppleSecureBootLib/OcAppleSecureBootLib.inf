## @file
#
#  Component description file for the library producing the Apple Secure Boot protocol.
#
#  Copyright (C) 2019, Download-Fritz. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleSecureBootLib
  FILE_GUID                      = 5F7347FB-56B9-4183-9C5D-064CA5F1B8E3
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleSecureBootLib|DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER DXE_SAL_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAppleSecureBootLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Guids]
  gAppleSecureBootVariableGuid  ## SOMETIMES_CONSUMES
  gAppleVendorVariableGuid      ## SOMETIMES_CONSUMES

[Protocols]
  gAppleSecureBootProtocolGuid        ## SOMETIMES_PRODUCES
  gAppleImg4VerificationProtocolGuid  ## SOMETIMES_CONSUMES
  gEfiSimpleFileSystemProtocolGuid    ## SOMETIMES_CONSUMES

[LibraryClasses]
  BaseMemoryLib
  BaseOverflowLib
  DebugLib
  MemoryAllocationLib
  OcDevicePathLib
  OcFileLib
  OcMiscLib
  OcStringLib
  PrintLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
