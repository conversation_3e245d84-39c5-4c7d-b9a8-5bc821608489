## @file
#
#  Component description file for the library producing the Apple Image Conversion protocol.
#
#  Copyright (C) 2018 savvas. All rights reserved.<BR>
#  Portions copyright (C) 2016 slice. All rights reserved.<BR>
#  Portions copyright (C) 2018 vit9696. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleImageConversionLib
  FILE_GUID                      = 2482B69C-3AD6-4AAE-9817-1173F763DF20
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleImageConversionLib|DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER DXE_SAL_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAppleImageConversionLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Protocols]
  gAppleImageConversionProtocolGuid   ## SOMETIMES_PRODUCES

[LibraryClasses]
  BaseMemoryLib
  BaseOverflowLib
  DebugLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  OcMiscLib
  OcPngLib
