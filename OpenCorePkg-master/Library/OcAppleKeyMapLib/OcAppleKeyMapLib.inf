## @file
#
#  Component description file for the library producing the Apple Event protocol.
#
#  Copyright (C) 2019, Download-Fritz. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleKeyMapLib
  FILE_GUID                      = 3A5CA054-2A65-4A00-AE58-B61BAFD6CF9A
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleKeyMapLib|DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAppleKeyMapLib.c
  UpDownDetection.c

[Packages]
  OpenCorePkg/OpenCorePkg.dec
  MdePkg/MdePkg.dec

[Protocols]
  gAppleKeyMapDatabaseProtocolGuid    ## SOMETIMES_PRODUCES
  gAppleKeyMapAggregatorProtocolGuid  ## SOMETIMES_PRODUCES

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  OcMiscLib
  MemoryAllocationLib
  UefiBootServicesTableLib
