## @file
#
#  Component description file for the library producing the Apple Device property protocol.
#
#  Copyright (C) 2019, vit9696. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleBootPolicyLib
  FILE_GUID                      = 330E9083-C008-4030-AA86-ECF1FBAE824D
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleBootPolicyLib|PEIM DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAppleBootPolicyLib.c

[Packages]
  OpenCorePkg/OpenCorePkg.dec
  MdePkg/MdePkg.dec

[Guids]
  gAppleApfsContainerInfoGuid        ## SOMETIMES_CONSUMES
  gAppleApfsVolumeInfoGuid           ## SOMETIMES_CONSUMES
  gAppleBlessedSystemFileInfoGuid    ## SOMETIMES_CONSUMES
  gAppleBlessedSystemFolderInfoGuid  ## SOMETIMES_CONSUMES
  gAppleBlessedOsxFolderInfoGuid     ## SOMETIMES_CONSUMES
  gEfiFileInfoGuid                   ## SOMETIMES_CONSUMES

[Protocols]
  gAppleBootPolicyProtocolGuid      ## PRODUCES
  gEfiSimpleFileSystemProtocolGuid  ## SOMETIMES_CONSUMES

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  BaseOverflowLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  PrintLib
  UefiBootServicesTableLib
  OcFileLib
  OcStringLib
  OcXmlLib
