## @file
# OcConsoleControlEntryModeLib
#
# Copyright (c) 2019, Download-Fritz.  All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcConsoleControlEntryModeGenericLib
  FILE_GUID                      = 74B78F70-2959-47C7-8EAB-62DED119972B
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  CONSTRUCTOR                    = OcConsoleControlEntryModeConstructor
  LIBRARY_CLASS                  = NULL|DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SAL_DRIVER DXE_SMM_DRIVER SMM_CORE UEFI_APPLICATION UEFI_DRIVER

#
#  VALID_ARCHITECTURES           = X64
#

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Pcd]
  gOpenCorePkgTokenSpaceGuid.PcdConsoleControlEntryMode

[Protocols]
  gEfiConsoleControlProtocolGuid

[LibraryClasses]
  PcdLib
  UefiBootServicesTableLib

[Sources]
  OcConsoleControlEntryModeLib.c
