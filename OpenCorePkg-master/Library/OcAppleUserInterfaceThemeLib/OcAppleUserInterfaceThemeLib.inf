## @file
#
#  Component description file for the library producing the Apple User Interface Theme protocol.
#
#  Copyright (c) 2016-2018, vit9696. All rights reserved.<BR>
#  Portions copyright (c) 2018, savvas. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleUserInterfaceThemeLib
  FILE_GUID                      = 9EC4BF49-61CB-4BB1-9D1A-7524800393FC
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleUserInterfaceThemeLib|DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER DXE_SAL_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  OcAppleUserInterfaceThemeLib.c

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[Guids]
  gAppleVendorVariableGuid            ## SOMETIMES_CONSUMES

[Protocols]
  gEfiUserInterfaceThemeProtocolGuid  ## SOMETIMES_PRODUCES

[LibraryClasses]
  DebugLib
  OcMiscLib
  UefiBootServicesTableLib
  UefiRuntimeServicesTableLib
