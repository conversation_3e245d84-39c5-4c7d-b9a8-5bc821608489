## @file
# Component description file for OcAppleImg4Lib.
#
# Copyright (C) 2019, Download-Fritz. All rights reserved.<BR>
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION     = 0x00010005
  BASE_NAME       = OcAppleImg4Lib
  FILE_GUID       = 3AFDED15-4E18-4FF1-9C5C-0D1F259DCF56
  MODULE_TYPE     = BASE
  VERSION_STRING  = 1.0
  LIBRARY_CLASS   = OcAppleImg4Lib

[Packages]
  MdePkg/MdePkg.dec
  OpenCorePkg/OpenCorePkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib
  OcAppleKeysLib
  UefiRuntimeServicesTableLib

[Sources]
  libDER/asn1Types.h
  libDER/DER_CertCrl.c
  libDER/DER_CertCrl.h
  libDER/DER_Decode.c
  libDER/DER_Decode.h
  libDER/DER_Digest.c
  libDER/DER_Digest.h
  #libDER/DER_Encode.c
  libDER/DER_Encode.h
  libDER/DER_Keys.c
  libDER/DER_Keys.h
  libDER/libDER.h
  libDER_config.h
  libDER/oids.c
  libDER/oids.h
  libDERImg4/DER_Img4Manifest.c
  libDERImg4/DER_Img4Manifest.h
  libDERImg4/Img4oids.c
  libDERImg4/Img4oids.h
  libDERImg4/libDERImg4.h
  libDERImg4/libDERImg4_config.h
  OcAppleImg4Lib.c
