## @file
#
#  Component description file for the library producing the Apple Event protocol.
#
#  Copyright (C) 2019, Download-Fritz. All rights reserved.<BR>
#
# All rights reserved.
#
# This program and the accompanying materials
# are licensed and made available under the terms and conditions of the BSD License
# which accompanies this distribution.  The full text of the license may be found at
# http://opensource.org/licenses/bsd-license.php
#
# THE PROGRAM IS DISTRIBUTED UNDER THE BSD LICENSE ON AN "AS IS" BASIS,
# WITHOUT WARRANTIES OR REPRESENTATIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED.
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = OcAppleEventLib
  FILE_GUID                      = 4587D92C-0225-4D5C-8E6F-D86303202001
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = OcAppleEventLib|DXE_DRIVER DXE_RUNTIME_DRIVER UEFI_DRIVER UEFI_APPLICATION DXE_SMM_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 IPF EBC
#

[Sources]
  AppleEventInternal.h
  AppleKeyMap.c
  EventQueue.c
  KeyHandler.c
  OcAppleEventLib.c
  PointerHandler.c

[Packages]
  OpenCorePkg/OpenCorePkg.dec
  MdePkg/MdePkg.dec

[Protocols]
  gAppleEventProtocolGuid             ## SOMETIMES_PRODUCES
  gAppleKeyMapDatabaseProtocolGuid    ## SOMETIMES_CONSUMES
  gAppleKeyMapAggregatorProtocolGuid  ## SOMETIMES_CONSUMES
  gEfiConsoleControlProtocolGuid      ## SOMETIMES_CONSUMES
  gEfiGraphicsOutputProtocolGuid      ## SOMETIMES_CONSUMES
  gEfiUgaDrawProtocolGuid             ## SOMETIMES_CONSUMES
  gEfiSimplePointerProtocolGuid       ## SOMETIMES_CONSUMES

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  OcMiscLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiLib
  UefiRuntimeServicesTableLib
