#!/bin/bash
#
# Build Script for Generic ARM64 XNU Kernel
#
# This script builds XNU kernel with patches for generic ARM64 hardware
# compatibility, including all security bypasses and hardware abstraction
# modifications needed for non-Apple Silicon ARM64 systems.

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${SCRIPT_DIR}/build-generic-arm64"
INSTALL_DIR="${SCRIPT_DIR}/install-generic-arm64"
PATCHES_DIR="${SCRIPT_DIR}/xnu-generic-arm64-patches"

# Source directories
XNU_SOURCE_DIR="${XNU_SOURCE_DIR:-./xnu}"
DTRACE_SOURCE_DIR="${DTRACE_SOURCE_DIR:-./dtrace}"
AVAILABILITY_VERSIONS_DIR="${AVAILABILITY_VERSIONS_DIR:-./AvailabilityVersions}"

# Build configuration
PARALLEL_JOBS="${PARALLEL_JOBS:-$(nproc)}"
BUILD_TYPE="${BUILD_TYPE:-RELEASE}"
TARGET_ARCH="ARM64"
TARGET_PLATFORM="GENERIC_ARM64"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites for generic ARM64 XNU build..."
    
    local missing_tools=()
    
    # Check required tools
    local required_tools=(
        "git"
        "make"
        "clang"
        "clang++"
        "patch"
        "python3"
        "xcodebuild"
        "xcrun"
    )
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install Xcode and command line tools"
        return 1
    fi
    
    # Check for ARM64 cross-compilation support
    if ! xcrun --sdk macosx --show-sdk-path &> /dev/null; then
        log_error "macOS SDK not found"
        return 1
    fi
    
    log_success "Prerequisites check passed"
    return 0
}

# Function to setup build environment
setup_build_environment() {
    log_info "Setting up build environment for generic ARM64..."
    
    # Create build directories
    mkdir -p "${BUILD_DIR}"/{xnu,dtrace,tools}
    mkdir -p "${INSTALL_DIR}"/{kernel,symbols,headers}
    
    # Set environment variables for XNU build
    export ARCH_CONFIGS="ARM64"
    export KERNEL_CONFIGS="RELEASE"
    export TARGET_CONFIGS="GENERIC_ARM64"
    export MACHINE_CONFIG="GENERIC_ARM64"
    
    # Compiler settings
    export CC="$(xcrun -find clang)"
    export CXX="$(xcrun -find clang++)"
    export LD="$(xcrun -find ld)"
    export AR="$(xcrun -find ar)"
    export STRIP="$(xcrun -find strip)"
    export DSYMUTIL="$(xcrun -find dsymutil)"
    
    # SDK settings
    export SDKROOT="$(xcrun --sdk macosx --show-sdk-path)"
    export MACOSX_DEPLOYMENT_TARGET="11.0"
    
    # Build flags
    export CFLAGS="-arch arm64 -isysroot ${SDKROOT} -mmacosx-version-min=11.0"
    export CXXFLAGS="-arch arm64 -isysroot ${SDKROOT} -mmacosx-version-min=11.0"
    export LDFLAGS="-arch arm64 -isysroot ${SDKROOT}"
    
    # Generic ARM64 specific flags
    export GENERIC_ARM64_BUILD=1
    export DISABLE_APPLE_SILICON_FEATURES=1
    export ENABLE_SECURITY_BYPASS=1
    
    log_success "Build environment setup complete"
}

# Function to apply XNU patches
apply_xnu_patches() {
    log_info "Applying XNU patches for generic ARM64 compatibility..."
    
    if [[ ! -d "${XNU_SOURCE_DIR}" ]]; then
        log_error "XNU source directory not found: ${XNU_SOURCE_DIR}"
        return 1
    fi
    
    cd "${XNU_SOURCE_DIR}"
    
    # Apply patches in order
    local patches=(
        "0001-disable-pointer-authentication.patch"
        "0002-disable-page-protection-layer.patch"
        "0003-disable-amfi-code-signing.patch"
        "0004-disable-sep-secure-boot.patch"
        "0005-implement-smc-stubs.patch"
        "0006-modify-apple-arm-platform.patch"
        "0007-implement-hardware-spoofing.patch"
        "0008-low-level-arm64-support.patch"
        "0009-kext-modifications.patch"
        "0010-add-hfsplus-support.patch"
        "0011-add-apple-partition-support.patch"
    )
    
    for patch in "${patches[@]}"; do
        local patch_file="${PATCHES_DIR}/${patch}"
        if [[ -f "$patch_file" ]]; then
            log_info "Applying patch: $(basename "$patch")"
            if ! patch -p1 < "$patch_file"; then
                log_error "Failed to apply patch: $patch"
                return 1
            fi
        else
            log_warning "Patch not found: $patch"
        fi
    done
    
    cd "${SCRIPT_DIR}"
    log_success "XNU patches applied successfully"
}

# Function to build XNU kernel
build_xnu_kernel() {
    log_info "Building XNU kernel for generic ARM64..."
    
    cd "${XNU_SOURCE_DIR}"
    
    # Clean previous build
    make clean || true
    
    # Build XNU with generic ARM64 configuration
    log_info "Starting XNU kernel build..."
    
    # Set build variables
    export BUILD_WERROR=0
    export BUILD_LTO=0
    export BUILD_STATIC_LINK=0
    
    # Build command
    local build_cmd=(
        make
        -j"${PARALLEL_JOBS}"
        ARCH_CONFIGS="ARM64"
        KERNEL_CONFIGS="${BUILD_TYPE}"
        TARGET_CONFIGS="${TARGET_PLATFORM}"
        MACHINE_CONFIG="${TARGET_PLATFORM}"
        BUILD_WERROR=0
        BUILD_LTO=0
        GENERIC_ARM64_BUILD=1
        DISABLE_APPLE_SILICON_FEATURES=1
        ENABLE_SECURITY_BYPASS=1
    )
    
    if ! "${build_cmd[@]}"; then
        log_error "XNU kernel build failed"
        return 1
    fi
    
    # Find built kernel
    local kernel_path="BUILD/obj/${TARGET_PLATFORM}_${TARGET_ARCH}/${BUILD_TYPE}/kernel"
    if [[ -f "$kernel_path" ]]; then
        # Copy built kernel
        cp "$kernel_path" "${INSTALL_DIR}/kernel/kernel.generic-arm64"
        
        # Copy debug symbols if available
        local dsym_path="BUILD/obj/${TARGET_PLATFORM}_${TARGET_ARCH}/${BUILD_TYPE}/kernel.dSYM"
        if [[ -d "$dsym_path" ]]; then
            cp -r "$dsym_path" "${INSTALL_DIR}/symbols/"
        fi
        
        log_success "XNU kernel built and installed"
    else
        log_error "Built kernel not found at $kernel_path"
        return 1
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to build kernel headers
build_kernel_headers() {
    log_info "Building kernel headers for generic ARM64..."
    
    cd "${XNU_SOURCE_DIR}"
    
    # Build headers
    if ! make installhdrs ARCH_CONFIGS="ARM64" TARGET_CONFIGS="${TARGET_PLATFORM}" DSTROOT="${INSTALL_DIR}/headers"; then
        log_error "Kernel headers build failed"
        return 1
    fi
    
    log_success "Kernel headers built and installed"
    cd "${SCRIPT_DIR}"
}

# Function to create installation package
create_installation_package() {
    log_info "Creating installation package..."
    
    local package_dir="${BUILD_DIR}/generic-arm64-xnu-package"
    mkdir -p "$package_dir"
    
    # Copy kernel and related files
    cp -r "${INSTALL_DIR}"/* "$package_dir/"
    
    # Create installation script
    cat > "$package_dir/install.sh" << 'EOF'
#!/bin/bash
#
# Generic ARM64 XNU Installation Script
#

set -e

INSTALL_ROOT="${INSTALL_ROOT:-/}"
KERNEL_DIR="${INSTALL_ROOT}/System/Library/Kernels"
HEADERS_DIR="${INSTALL_ROOT}/System/Library/Frameworks/Kernel.framework"

echo "Installing Generic ARM64 XNU Kernel..."

# Backup original kernel
if [[ -f "${KERNEL_DIR}/kernel" ]]; then
    sudo cp "${KERNEL_DIR}/kernel" "${KERNEL_DIR}/kernel.original.backup"
    echo "Original kernel backed up"
fi

# Install new kernel
sudo mkdir -p "${KERNEL_DIR}"
sudo cp kernel/kernel.generic-arm64 "${KERNEL_DIR}/kernel"
sudo chmod 755 "${KERNEL_DIR}/kernel"

echo "Generic ARM64 XNU kernel installed"

# Install headers if available
if [[ -d "headers" ]]; then
    sudo cp -r headers/* "${HEADERS_DIR}/"
    echo "Kernel headers installed"
fi

echo "Installation complete!"
echo ""
echo "WARNING: This kernel has security features disabled!"
echo "Only use on systems where you understand the security implications."
echo ""
echo "Reboot to use the new kernel."
EOF
    
    chmod +x "$package_dir/install.sh"
    
    # Create README
    cat > "$package_dir/README.md" << 'EOF'
# Generic ARM64 XNU Kernel Package

This package contains a modified XNU kernel that can run on generic ARM64 
hardware (non-Apple Silicon).

## Contents

- `kernel/kernel.generic-arm64` - Modified XNU kernel
- `symbols/` - Debug symbols (if available)
- `headers/` - Kernel headers
- `install.sh` - Installation script

## Installation

1. Run the installation script as root:
   ```bash
   sudo ./install.sh
   ```

2. Reboot your system

## Security Warning

**This kernel has ALL security features disabled, including:**
- Pointer Authentication (PAC)
- Page Protection Layer (PPL)
- Apple Mobile File Integrity (AMFI)
- Secure Enclave Processor (SEP)
- Secure Boot verification

**Only use this in controlled environments for development and testing.**

## Hardware Spoofing

The kernel includes hardware spoofing to appear as Apple Silicon:
- Default spoofing: M1 MacBook Air
- Configurable via SMC interface
- Provides fake sensor readings

## Compatibility

Tested on:
- Generic ARM64 development boards
- ARM64 virtual machines
- Standard ARM64 servers

Not compatible with:
- Apple Silicon Macs (use original kernel)
- ARM32 systems
- x86_64 systems
EOF
    
    # Create tarball
    cd "${BUILD_DIR}"
    tar -czf "generic-arm64-xnu-$(date +%Y%m%d).tar.gz" generic-arm64-xnu-package/
    
    cd "${SCRIPT_DIR}"
    log_success "Package created: ${BUILD_DIR}/generic-arm64-xnu-$(date +%Y%m%d).tar.gz"
}

# Function to run tests
run_tests() {
    log_info "Running basic tests..."
    
    local kernel_file="${INSTALL_DIR}/kernel/kernel.generic-arm64"
    
    if [[ ! -f "$kernel_file" ]]; then
        log_error "Kernel file not found for testing"
        return 1
    fi
    
    # Check kernel file
    log_info "Checking kernel file..."
    file "$kernel_file"
    
    # Check for required symbols
    log_info "Checking for generic ARM64 symbols..."
    if nm "$kernel_file" 2>/dev/null | grep -q "generic_arm64_mode"; then
        log_success "Generic ARM64 symbols found"
    else
        log_warning "Generic ARM64 symbols not found"
    fi
    
    # Check for security bypass symbols
    if nm "$kernel_file" 2>/dev/null | grep -q "security_bypass"; then
        log_success "Security bypass symbols found"
    else
        log_warning "Security bypass symbols not found"
    fi
    
    log_success "Basic tests completed"
}

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build script for Generic ARM64 XNU kernel.

Options:
    -h, --help          Show this help message
    --clean             Clean build directories before building
    --headers-only      Build only kernel headers
    --no-tests          Skip running tests
    --no-package        Skip creating package

Environment Variables:
    XNU_SOURCE_DIR      Path to XNU source directory (default: ./xnu)
    PARALLEL_JOBS       Number of parallel build jobs (default: nproc)
    BUILD_TYPE          Build type: RELEASE or DEBUG (default: RELEASE)

Examples:
    # Full build
    $0
    
    # Clean build
    $0 --clean
    
    # Headers only
    $0 --headers-only
    
    # Custom source directory
    XNU_SOURCE_DIR=/path/to/xnu $0

EOF
}

# Main function
main() {
    local clean_build=false
    local headers_only=false
    local run_tests_flag=true
    local create_package_flag=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            --clean)
                clean_build=true
                shift
                ;;
            --headers-only)
                headers_only=true
                shift
                ;;
            --no-tests)
                run_tests_flag=false
                shift
                ;;
            --no-package)
                create_package_flag=false
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting Generic ARM64 XNU build system"
    log_info "Build directory: ${BUILD_DIR}"
    log_info "Install directory: ${INSTALL_DIR}"
    echo
    
    # Clean if requested
    if [[ "$clean_build" == true ]]; then
        log_info "Cleaning build directories..."
        rm -rf "${BUILD_DIR}" "${INSTALL_DIR}"
    fi
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Setup build environment
    setup_build_environment
    
    # Apply patches
    apply_xnu_patches
    
    # Build components
    if [[ "$headers_only" == true ]]; then
        build_kernel_headers
    else
        build_xnu_kernel
        build_kernel_headers
    fi
    
    # Run tests
    if [[ "$run_tests_flag" == true && "$headers_only" == false ]]; then
        run_tests
    fi
    
    # Create package
    if [[ "$create_package_flag" == true && "$headers_only" == false ]]; then
        create_installation_package
    fi
    
    log_success "Build completed successfully!"
    log_info "Installation directory: ${INSTALL_DIR}"
    
    if [[ "$create_package_flag" == true && "$headers_only" == false ]]; then
        log_info "Package created in: ${BUILD_DIR}"
    fi
    
    echo
    log_warning "SECURITY WARNING:"
    log_warning "This kernel has ALL security features disabled!"
    log_warning "Only use in controlled environments for development/testing."
}

# Run main function
main "$@"
