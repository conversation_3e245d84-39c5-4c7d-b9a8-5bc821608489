# GRUB Configuration for Tahoe TechRechard ISO Boot
# ARM64 GRUB with XNU Patching Support
# 
# This configuration boots Tahoe TechRechard ISO from the untitled folder
# using the compiled ARM64 GRUB with generic ARM64 XNU patches

# Set timeout for menu selection
set timeout=10
set default=0

# Load required modules for Tahoe TechRechard boot
insmod part_gpt
insmod part_apple
insmod hfsplus
insmod hfsplus_generic_arm64
insmod iso9660
insmod udf
insmod fat
insmod efi_gop
insmod efi_uga
insmod video_bochs
insmod video_cirrus
insmod font
insmod gfxterm
insmod png
insmod jpeg

# Set graphics mode for better display
if loadfont unicode ; then
  set gfxmode=auto
  insmod all_video
  terminal_output gfxterm
fi

# Set GRUB theme (optional)
set theme=/boot/grub/themes/starfield/theme.txt

# Function to detect Tahoe TechRechard ISO
function find_tahoe_iso {
    # Search for Tahoe TechRechard ISO in common locations
    for device in (hd0) (hd1) (hd2) (hd3) (cd0) (cd1); do
        # Check untitled folder first
        if [ -f "${device}/untitled/TechRechard.iso" ]; then
            set tahoe_device="${device}"
            set tahoe_path="/untitled/TechRechard.iso"
            return 0
        fi
        
        # Check for various Tahoe ISO names
        for iso_name in "TechRechard.iso" "Tahoe.iso" "macOS_Tahoe.iso" "macOS-Tahoe-TechRechard.iso"; do
            if [ -f "${device}/untitled/${iso_name}" ]; then
                set tahoe_device="${device}"
                set tahoe_path="/untitled/${iso_name}"
                return 0
            fi
            
            # Also check root directory
            if [ -f "${device}/${iso_name}" ]; then
                set tahoe_device="${device}"
                set tahoe_path="/${iso_name}"
                return 0
            fi
        done
    done
    return 1
}

# Main menu entry for Tahoe TechRechard
menuentry "macOS Tahoe TechRechard (Generic ARM64)" --class macos --class darwin --class os {
    echo "Loading Tahoe TechRechard ISO..."
    
    # Find the Tahoe ISO
    find_tahoe_iso
    if [ $? -ne 0 ]; then
        echo "Error: Tahoe TechRechard ISO not found!"
        echo "Please ensure the ISO is in the 'untitled' folder"
        echo "Supported locations:"
        echo "  /untitled/TechRechard.iso"
        echo "  /untitled/Tahoe.iso"
        echo "  /untitled/macOS_Tahoe.iso"
        echo "  /untitled/macOS-Tahoe-TechRechard.iso"
        sleep 5
        return
    fi
    
    echo "Found Tahoe ISO: ${tahoe_device}${tahoe_path}"
    
    # Set root device
    set root="${tahoe_device}"
    
    # Load the ISO
    loopback loop "${tahoe_path}"
    set root=(loop)
    
    # Enable generic ARM64 mode
    set generic_arm64_mode=1
    set disable_apple_silicon_features=1
    set enable_security_bypass=1
    
    # Set kernel boot arguments for Tahoe TechRechard
    set kernel_args="generic_arm64_mode=1 disable_amfi=1 disable_sep=1 disable_ppl=1 cs_enforcement_disable=1 amfi_get_out_of_my_way=1 -v"
    
    # Additional Tahoe-specific arguments
    set kernel_args="${kernel_args} tahoe_mode=1 techrechard_compat=1"
    
    # Hardware spoofing for compatibility
    set kernel_args="${kernel_args} spoof_board_id=J313AP spoof_chip_id=T8103"
    
    # Boot arguments for generic ARM64
    set kernel_args="${kernel_args} rootless=0 SIP=0 csr-active-config=0x67"
    
    # Try to find and load the XNU kernel from ISO
    if [ -f "/System/Library/Kernels/kernel" ]; then
        echo "Loading XNU kernel from Tahoe ISO..."
        xnu_kernel /System/Library/Kernels/kernel ${kernel_args}
    elif [ -f "/mach_kernel" ]; then
        echo "Loading legacy mach_kernel from Tahoe ISO..."
        xnu_kernel /mach_kernel ${kernel_args}
    elif [ -f "/kernelcache" ]; then
        echo "Loading kernelcache from Tahoe ISO..."
        xnu_kernel /kernelcache ${kernel_args}
    else
        echo "Error: No XNU kernel found in Tahoe ISO!"
        echo "Trying alternative boot method..."
        
        # Try EFI boot
        if [ -f "/System/Library/CoreServices/boot.efi" ]; then
            echo "Loading boot.efi from Tahoe ISO..."
            chainloader /System/Library/CoreServices/boot.efi
        elif [ -f "/EFI/BOOT/BOOTAA64.EFI" ]; then
            echo "Loading BOOTAA64.EFI from Tahoe ISO..."
            chainloader /EFI/BOOT/BOOTAA64.EFI
        else
            echo "Error: No bootable files found in Tahoe ISO!"
            sleep 5
            return
        fi
    fi
    
    echo "Booting Tahoe TechRechard..."
    boot
}

# Alternative boot method using EFI chainloader
menuentry "macOS Tahoe TechRechard (EFI Boot)" --class macos --class darwin --class os {
    echo "Loading Tahoe TechRechard ISO (EFI Method)..."
    
    # Find the Tahoe ISO
    find_tahoe_iso
    if [ $? -ne 0 ]; then
        echo "Error: Tahoe TechRechard ISO not found!"
        sleep 5
        return
    fi
    
    echo "Found Tahoe ISO: ${tahoe_device}${tahoe_path}"
    
    # Set root device and load ISO
    set root="${tahoe_device}"
    loopback loop "${tahoe_path}"
    set root=(loop)
    
    # Try EFI boot methods
    if [ -f "/System/Library/CoreServices/boot.efi" ]; then
        echo "Chainloading boot.efi..."
        chainloader /System/Library/CoreServices/boot.efi
    elif [ -f "/EFI/BOOT/BOOTAA64.EFI" ]; then
        echo "Chainloading BOOTAA64.EFI..."
        chainloader /EFI/BOOT/BOOTAA64.EFI
    else
        echo "Error: No EFI bootloader found!"
        sleep 5
        return
    fi
    
    boot
}

# Recovery mode boot
menuentry "macOS Tahoe TechRechard (Recovery Mode)" --class macos --class darwin --class os {
    echo "Loading Tahoe TechRechard in Recovery Mode..."
    
    # Find the Tahoe ISO
    find_tahoe_iso
    if [ $? -ne 0 ]; then
        echo "Error: Tahoe TechRechard ISO not found!"
        sleep 5
        return
    fi
    
    # Set root device and load ISO
    set root="${tahoe_device}"
    loopback loop "${tahoe_path}"
    set root=(loop)
    
    # Recovery mode arguments
    set recovery_args="generic_arm64_mode=1 disable_amfi=1 disable_sep=1 disable_ppl=1 cs_enforcement_disable=1 -v -s"
    set recovery_args="${recovery_args} recovery_mode=1 tahoe_recovery=1"
    
    # Try to boot recovery
    if [ -f "/System/Library/CoreServices/boot.efi" ]; then
        echo "Loading recovery boot.efi..."
        chainloader /System/Library/CoreServices/boot.efi
    else
        echo "Error: Recovery bootloader not found!"
        sleep 5
        return
    fi
    
    boot
}

# Verbose boot for debugging
menuentry "macOS Tahoe TechRechard (Verbose Debug)" --class macos --class darwin --class os {
    echo "Loading Tahoe TechRechard with verbose debugging..."
    
    # Find the Tahoe ISO
    find_tahoe_iso
    if [ $? -ne 0 ]; then
        echo "Error: Tahoe TechRechard ISO not found!"
        sleep 5
        return
    fi
    
    # Set root device and load ISO
    set root="${tahoe_device}"
    loopback loop "${tahoe_path}"
    set root=(loop)
    
    # Verbose debug arguments
    set debug_args="generic_arm64_mode=1 disable_amfi=1 disable_sep=1 disable_ppl=1 cs_enforcement_disable=1"
    set debug_args="${debug_args} -v -s debug=0x144 kext-dev-mode=1 rootless=0"
    set debug_args="${debug_args} tahoe_debug=1 techrechard_debug=1"
    
    # Try XNU kernel with debug args
    if [ -f "/System/Library/Kernels/kernel" ]; then
        echo "Loading XNU kernel with debug arguments..."
        xnu_kernel /System/Library/Kernels/kernel ${debug_args}
    else
        echo "Falling back to EFI boot with debug..."
        if [ -f "/System/Library/CoreServices/boot.efi" ]; then
            chainloader /System/Library/CoreServices/boot.efi
        fi
    fi
    
    boot
}

# Safe mode boot
menuentry "macOS Tahoe TechRechard (Safe Mode)" --class macos --class darwin --class os {
    echo "Loading Tahoe TechRechard in Safe Mode..."
    
    # Find the Tahoe ISO
    find_tahoe_iso
    if [ $? -ne 0 ]; then
        echo "Error: Tahoe TechRechard ISO not found!"
        sleep 5
        return
    fi
    
    # Set root device and load ISO
    set root="${tahoe_device}"
    loopback loop "${tahoe_path}"
    set root=(loop)
    
    # Safe mode arguments
    set safe_args="generic_arm64_mode=1 disable_amfi=1 disable_sep=1 disable_ppl=1 cs_enforcement_disable=1"
    set safe_args="${safe_args} -x -v safe_mode=1 tahoe_safe=1"
    
    # Boot in safe mode
    if [ -f "/System/Library/Kernels/kernel" ]; then
        echo "Loading XNU kernel in safe mode..."
        xnu_kernel /System/Library/Kernels/kernel ${safe_args}
    else
        if [ -f "/System/Library/CoreServices/boot.efi" ]; then
            chainloader /System/Library/CoreServices/boot.efi
        fi
    fi
    
    boot
}

# Utility: List available ISOs
menuentry "List Available ISOs" --class utility {
    echo "Scanning for available ISOs..."
    echo ""
    
    for device in (hd0) (hd1) (hd2) (hd3) (cd0) (cd1); do
        echo "Checking device: ${device}"
        
        # Check untitled folder
        if [ -d "${device}/untitled" ]; then
            echo "  Found untitled folder on ${device}"
            ls "${device}/untitled/"
        fi
        
        # Check root for ISOs
        for file in "${device}/*.iso"; do
            if [ -f "${file}" ]; then
                echo "  Found ISO: ${file}"
            fi
        done
        echo ""
    done
    
    echo "Press any key to return to menu..."
    read
}

# Utility: GRUB command line
menuentry "GRUB Command Line" --class utility {
    echo "Entering GRUB command line..."
    echo "Type 'exit' to return to menu"
    echo ""
    terminal_input console
    terminal_output console
}

# Reboot option
menuentry "Reboot" --class reboot {
    echo "Rebooting system..."
    reboot
}

# Shutdown option
menuentry "Shutdown" --class shutdown {
    echo "Shutting down system..."
    halt
}
