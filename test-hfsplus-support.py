#!/usr/bin/env python3
"""
HFS+ Support Testing Framework for Generic ARM64 XNU

This script tests HFS+ filesystem support in the generic ARM64 XNU kernel
to ensure it can properly read and boot from Apple-formatted drives.
"""

import os
import sys
import subprocess
import tempfile
import argparse
from pathlib import Path

class HFSPlusTester:
    def __init__(self, config):
        self.config = config
        self.test_results = {}
        self.kernel_path = config.get('kernel_path')
        
    def run_hfsplus_tests(self):
        """Run HFS+ support tests"""
        print("Starting HFS+ Support Test Suite")
        print("=" * 40)
        
        tests = [
            self.test_hfsplus_symbols,
            self.test_apple_partition_symbols,
            self.test_hfsplus_mount_support,
            self.test_apple_partition_map_support,
            self.test_case_sensitivity_support,
            self.test_journal_support,
            self.test_boot_support
        ]
        
        for test in tests:
            print(f"\nRunning {test.__name__}...")
            try:
                result = test()
                self.test_results[test.__name__] = result
                if result['status'] == 'PASSED':
                    print(f"✓ {test.__name__}: PASSED")
                else:
                    print(f"✗ {test.__name__}: FAILED")
                    if 'error' in result:
                        print(f"  Error: {result['error']}")
            except Exception as e:
                print(f"✗ {test.__name__}: ERROR - {e}")
                self.test_results[test.__name__] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
        
        self.generate_hfsplus_report()
        return self.get_overall_result()
    
    def test_hfsplus_symbols(self):
        """Test for HFS+ filesystem symbols"""
        result = {'status': 'RUNNING', 'details': {}}
        
        required_symbols = [
            'hfsplus_init_generic_arm64',
            'hfsplus_is_enabled_generic_arm64',
            'hfsplus_detect_volume_generic_arm64',
            'hfsplus_mount_generic_arm64',
            'hfsplus_boot_support_generic_arm64'
        ]
        
        try:
            nm_output = subprocess.check_output(['nm', self.kernel_path], 
                                              text=True, stderr=subprocess.DEVNULL)
            
            for symbol in required_symbols:
                if symbol in nm_output:
                    result['details'][symbol] = True
                else:
                    result['details'][symbol] = False
            
            if all(result['details'].values()):
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                missing = [s for s, found in result['details'].items() if not found]
                result['error'] = f"Missing HFS+ symbols: {missing}"
                
        except subprocess.CalledProcessError as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check symbols: {e}"
        
        return result
    
    def test_apple_partition_symbols(self):
        """Test for Apple Partition Map symbols"""
        result = {'status': 'RUNNING', 'details': {}}
        
        required_symbols = [
            'IOApplePartitionScheme',
            'scanApplePartitionMap',
            'isAppleHFSPartition',
            'isAppleAPFSPartition',
            'createMediaForApplePartition'
        ]
        
        try:
            nm_output = subprocess.check_output(['nm', self.kernel_path], 
                                              text=True, stderr=subprocess.DEVNULL)
            
            for symbol in required_symbols:
                if symbol in nm_output:
                    result['details'][symbol] = True
                else:
                    result['details'][symbol] = False
            
            if any(result['details'].values()):
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = "No Apple Partition Map symbols found"
                
        except subprocess.CalledProcessError as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check symbols: {e}"
        
        return result
    
    def test_hfsplus_mount_support(self):
        """Test HFS+ mount support"""
        result = {'status': 'RUNNING', 'details': {}}
        
        # Check for HFS+ mount-related strings
        mount_indicators = [
            b'HFS+ volume mounted successfully',
            b'Generic ARM64 compatibility mode enabled',
            b'HFS+ filesystem support',
            b'Apple filesystem compatibility'
        ]
        
        try:
            with open(self.kernel_path, 'rb') as f:
                kernel_data = f.read()
            
            for indicator in mount_indicators:
                if indicator in kernel_data:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = True
                else:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = False
            
            if any(result['details'].values()):
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = "No HFS+ mount support indicators found"
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check mount support: {e}"
        
        return result
    
    def test_apple_partition_map_support(self):
        """Test Apple Partition Map support"""
        result = {'status': 'RUNNING', 'details': {}}
        
        # Check for Apple Partition Map strings
        apm_indicators = [
            b'Apple Partition Map',
            b'Apple_HFS',
            b'Apple_HFSX',
            b'Apple_APFS',
            b'Apple_Boot',
            b'Apple_Recovery'
        ]
        
        try:
            with open(self.kernel_path, 'rb') as f:
                kernel_data = f.read()
            
            for indicator in apm_indicators:
                if indicator in kernel_data:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = True
                else:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = False
            
            found_count = sum(1 for found in result['details'].values() if found)
            if found_count >= 3:  # At least 3 indicators should be present
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = f"Insufficient Apple Partition Map support: {found_count}/6"
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check Apple Partition Map support: {e}"
        
        return result
    
    def test_case_sensitivity_support(self):
        """Test HFS+ case sensitivity support"""
        result = {'status': 'RUNNING', 'details': {}}
        
        # Check for case sensitivity handling
        case_indicators = [
            b'Case-sensitive HFS+',
            b'Case-insensitive HFS+',
            b'hfsplus_case_sensitivity_generic_arm64'
        ]
        
        try:
            with open(self.kernel_path, 'rb') as f:
                kernel_data = f.read()
            
            for indicator in case_indicators:
                if indicator in kernel_data:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = True
                else:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = False
            
            if any(result['details'].values()):
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = "No case sensitivity support found"
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check case sensitivity support: {e}"
        
        return result
    
    def test_journal_support(self):
        """Test HFS+ journal support"""
        result = {'status': 'RUNNING', 'details': {}}
        
        # Check for journal-related functionality
        journal_indicators = [
            b'journal',
            b'Journal',
            b'HFS+ journal'
        ]
        
        try:
            with open(self.kernel_path, 'rb') as f:
                kernel_data = f.read()
            
            journal_count = 0
            for indicator in journal_indicators:
                if indicator in kernel_data:
                    journal_count += kernel_data.count(indicator)
            
            result['details']['journal_references'] = journal_count
            
            if journal_count > 0:
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = "No HFS+ journal support found"
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check journal support: {e}"
        
        return result
    
    def test_boot_support(self):
        """Test HFS+ boot support"""
        result = {'status': 'RUNNING', 'details': {}}
        
        # Check for boot-related functionality
        boot_indicators = [
            b'HFS+ boot support',
            b'boot from HFS+',
            b'hfsplus_boot_support_generic_arm64'
        ]
        
        try:
            with open(self.kernel_path, 'rb') as f:
                kernel_data = f.read()
            
            for indicator in boot_indicators:
                if indicator in kernel_data:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = True
                else:
                    result['details'][indicator.decode('utf-8', errors='ignore')] = False
            
            if any(result['details'].values()):
                result['status'] = 'PASSED'
            else:
                result['status'] = 'FAILED'
                result['error'] = "No HFS+ boot support found"
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = f"Failed to check boot support: {e}"
        
        return result
    
    def create_test_hfsplus_image(self):
        """Create a test HFS+ image for validation"""
        try:
            # Create a temporary HFS+ image for testing
            with tempfile.NamedTemporaryFile(suffix='.dmg', delete=False) as tmp:
                image_path = tmp.name
            
            # Create HFS+ image using hdiutil (if available)
            try:
                subprocess.run([
                    'hdiutil', 'create', '-size', '100m', '-fs', 'HFS+',
                    '-volname', 'TestHFS', image_path
                ], check=True, capture_output=True)
                
                print(f"Created test HFS+ image: {image_path}")
                return image_path
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("hdiutil not available, skipping HFS+ image creation")
                os.unlink(image_path)
                return None
                
        except Exception as e:
            print(f"Failed to create test HFS+ image: {e}")
            return None
    
    def generate_hfsplus_report(self):
        """Generate HFS+ test report"""
        print("\n" + "=" * 40)
        print("HFS+ SUPPORT TEST RESULTS")
        print("=" * 40)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result['status'] == 'PASSED')
        failed_tests = sum(1 for result in self.test_results.values() 
                          if result['status'] == 'FAILED')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        print()
        
        for test_name, result in self.test_results.items():
            status_symbol = "✓" if result['status'] == 'PASSED' else "✗"
            print(f"{status_symbol} {test_name}: {result['status']}")
            
            if 'details' in result:
                for detail_name, detail_value in result['details'].items():
                    if isinstance(detail_value, bool):
                        detail_symbol = "  ✓" if detail_value else "  ✗"
                        print(f"{detail_symbol} {detail_name}")
                    else:
                        print(f"  • {detail_name}: {detail_value}")
            
            if 'error' in result:
                print(f"  Error: {result['error']}")
        
        # Save detailed report
        os.makedirs('test-results', exist_ok=True)
        import json
        with open('test-results/hfsplus-support-test-report.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\nDetailed report saved to test-results/hfsplus-support-test-report.json")
    
    def get_overall_result(self):
        """Get overall test result"""
        if not self.test_results:
            return False
        
        return all(result['status'] == 'PASSED' for result in self.test_results.values())

def main():
    parser = argparse.ArgumentParser(description='HFS+ Support Testing Framework')
    parser.add_argument('--kernel', required=True, help='Path to generic ARM64 XNU kernel')
    parser.add_argument('--create-test-image', action='store_true', 
                       help='Create test HFS+ image for validation')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.kernel):
        print(f"Error: Kernel file not found: {args.kernel}")
        sys.exit(1)
    
    config = {
        'kernel_path': args.kernel,
        'verbose': args.verbose
    }
    
    tester = HFSPlusTester(config)
    
    if args.create_test_image:
        test_image = tester.create_test_hfsplus_image()
        if test_image:
            print(f"Test HFS+ image created: {test_image}")
        else:
            print("Failed to create test HFS+ image")
    
    success = tester.run_hfsplus_tests()
    
    if success:
        print("\n🎉 All HFS+ tests passed! Generic ARM64 XNU can boot from Apple drives.")
    else:
        print("\n❌ Some HFS+ tests failed. Please review the results above.")
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
