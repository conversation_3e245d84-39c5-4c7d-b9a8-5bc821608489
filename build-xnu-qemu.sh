#!/bin/bash
#
# Build Script for QEMU ARM64 XNU Boot System
#
# This script builds all components needed to boot macOS XNU kernel
# in QEMU ARM64 virtualization:
# 1. Patched XNU kernel
# 2. Modified GRUB bootloader with ARM64 XNU support
# 3. QEMU configuration and testing tools

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="${SCRIPT_DIR}/build"
INSTALL_DIR="${SCRIPT_DIR}/install"

# Source directories
XNU_SOURCE_DIR="${XNU_SOURCE_DIR:-./xnu}"
GRUB_SOURCE_DIR="${GRUB_SOURCE_DIR:-./grub}"

# Build configuration
PARALLEL_JOBS="${PARALLEL_JOBS:-$(nproc)}"
BUILD_TYPE="${BUILD_TYPE:-Release}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    local required_tools=(
        "git"
        "make"
        "gcc"
        "clang"
        "patch"
        "python3"
        "qemu-system-aarch64"
    )
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools and try again"
        return 1
    fi
    
    # Check for ARM64 cross-compilation tools
    if ! command -v aarch64-linux-gnu-gcc &> /dev/null; then
        log_warning "ARM64 cross-compilation tools not found"
        log_warning "You may need to install gcc-aarch64-linux-gnu"
    fi
    
    log_success "Prerequisites check passed"
    return 0
}

# Function to setup build environment
setup_build_environment() {
    log_info "Setting up build environment..."
    
    # Create build directories
    mkdir -p "${BUILD_DIR}"/{xnu,grub,tools}
    mkdir -p "${INSTALL_DIR}"/{bin,lib,share}
    
    # Set environment variables
    export CC=clang
    export CXX=clang++
    export CFLAGS="-O2 -g"
    export CXXFLAGS="-O2 -g"
    
    log_success "Build environment setup complete"
}

# Function to apply XNU patches
apply_xnu_patches() {
    log_info "Applying XNU patches for QEMU compatibility..."
    
    if [[ ! -d "${XNU_SOURCE_DIR}" ]]; then
        log_error "XNU source directory not found: ${XNU_SOURCE_DIR}"
        return 1
    fi
    
    cd "${XNU_SOURCE_DIR}"
    
    # Apply patches
    local patches=(
        "${SCRIPT_DIR}/xnu-patches/0001-arm64-remove-apple-silicon-dependencies.patch"
        "${SCRIPT_DIR}/xnu-patches/0002-devicetree-qemu-compatibility.patch"
        "${SCRIPT_DIR}/xnu-patches/0003-memory-management-qemu.patch"
        "${SCRIPT_DIR}/xnu-patches/0004-secure-boot-bypass.patch"
        "${SCRIPT_DIR}/xnu-patches/0005-disable-apple-security-features.patch"
    )
    
    for patch in "${patches[@]}"; do
        if [[ -f "$patch" ]]; then
            log_info "Applying patch: $(basename "$patch")"
            if ! patch -p1 < "$patch"; then
                log_error "Failed to apply patch: $patch"
                return 1
            fi
        else
            log_warning "Patch not found: $patch"
        fi
    done
    
    cd "${SCRIPT_DIR}"
    log_success "XNU patches applied successfully"
}

# Function to build XNU kernel
build_xnu_kernel() {
    log_info "Building XNU kernel for QEMU ARM64..."
    
    cd "${XNU_SOURCE_DIR}"
    
    # Configure XNU build for ARM64
    export ARCH=arm64
    export PLATFORM=QEMU
    export TARGET_CONFIGS="QEMU ARM64"
    
    # Build XNU
    log_info "Starting XNU kernel build..."
    if ! make -j"${PARALLEL_JOBS}" ARCH=arm64 PLATFORM=QEMU; then
        log_error "XNU kernel build failed"
        return 1
    fi
    
    # Copy built kernel
    local kernel_path="BUILD/obj/QEMU_ARM64/kernel"
    if [[ -f "$kernel_path" ]]; then
        cp "$kernel_path" "${INSTALL_DIR}/bin/kernel.patched"
        log_success "XNU kernel built and installed"
    else
        log_error "Built kernel not found at $kernel_path"
        return 1
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to build GRUB with ARM64 XNU support
build_grub_bootloader() {
    log_info "Building GRUB bootloader with ARM64 XNU support..."
    
    cd "${GRUB_SOURCE_DIR}"
    
    # Configure GRUB for ARM64 EFI with XNU support
    if [[ ! -f "configure" ]]; then
        log_info "Running GRUB autogen..."
        ./autogen.sh
    fi
    
    log_info "Configuring GRUB..."
    ./configure \
        --target=aarch64-linux-gnu \
        --with-platform=efi \
        --enable-arm64-xnu \
        --prefix="${INSTALL_DIR}" \
        --disable-werror
    
    # Build GRUB
    log_info "Building GRUB..."
    if ! make -j"${PARALLEL_JOBS}"; then
        log_error "GRUB build failed"
        return 1
    fi
    
    # Install GRUB
    log_info "Installing GRUB..."
    make install
    
    # Create GRUB EFI image
    log_info "Creating GRUB EFI image..."
    "${INSTALL_DIR}/bin/grub-mkimage" \
        --format=arm64-efi \
        --output="${INSTALL_DIR}/bin/grub-arm64-efi.fd" \
        --prefix=/EFI/GRUB \
        part_gpt part_msdos fat ext2 normal boot linux \
        configfile loopback chain efifwsetup efi_gop \
        efi_uga ls search search_label search_fs_uuid \
        search_fs_file gfxterm gfxterm_background \
        gfxterm_menu test all_video loadenv exfat \
        xnu_arm64 apple_dt apple_memory apple_cache \
        apple_bootargs xnu_kernel apple_efi apple_boot
    
    cd "${SCRIPT_DIR}"
    log_success "GRUB bootloader built and installed"
}

# Function to create QEMU configuration
create_qemu_config() {
    log_info "Creating QEMU configuration..."
    
    # Copy QEMU scripts
    cp "${SCRIPT_DIR}/qemu-config/qemu-macos-arm64.sh" "${INSTALL_DIR}/bin/"
    cp "${SCRIPT_DIR}/qemu-config/test-xnu-qemu.py" "${INSTALL_DIR}/bin/"
    
    # Make scripts executable
    chmod +x "${INSTALL_DIR}/bin/qemu-macos-arm64.sh"
    chmod +x "${INSTALL_DIR}/bin/test-xnu-qemu.py"
    
    # Create GRUB configuration
    mkdir -p "${INSTALL_DIR}/share/grub"
    cat > "${INSTALL_DIR}/share/grub/grub.cfg" << 'EOF'
# GRUB configuration for QEMU ARM64 XNU boot
set timeout=10
insmod xnu_arm64

set qnu_qemu_mode=1
set xnu_no_apple_silicon=1
set xnu_standard_arm64=1
set qemu_bypass_secure_boot=1
set no_secure_boot=1

menuentry "macOS XNU (QEMU ARM64)" {
    echo "Loading XNU kernel for QEMU ARM64..."
    xnu_kernel64 /kernel.patched qemu_bypass_secure_boot no_secure_boot -v debug=0x14e
    echo "Booting XNU kernel..."
    boot
}

menuentry "macOS XNU (QEMU ARM64) - Safe Mode" {
    echo "Loading XNU kernel in safe mode..."
    xnu_kernel64 /kernel.patched qemu_bypass_secure_boot no_secure_boot -v -s -x debug=0x14e
    echo "Booting XNU kernel in safe mode..."
    boot
}
EOF
    
    log_success "QEMU configuration created"
}

# Function to run tests
run_tests() {
    log_info "Running tests..."
    
    if [[ -f "${INSTALL_DIR}/bin/test-xnu-qemu.py" ]]; then
        python3 "${INSTALL_DIR}/bin/test-xnu-qemu.py" \
            --grub-efi "${INSTALL_DIR}/bin/grub-arm64-efi.fd" \
            --xnu-kernel "${INSTALL_DIR}/bin/kernel.patched"
    else
        log_warning "Test script not found, skipping tests"
    fi
}

# Function to create installation package
create_package() {
    log_info "Creating installation package..."
    
    local package_dir="${BUILD_DIR}/qemu-xnu-arm64-package"
    mkdir -p "$package_dir"
    
    # Copy binaries
    cp -r "${INSTALL_DIR}"/* "$package_dir/"
    
    # Create README
    cat > "$package_dir/README.md" << 'EOF'
# QEMU ARM64 XNU Boot Package

This package contains all components needed to boot macOS XNU kernel
in QEMU ARM64 virtualization environment.

## Contents

- `bin/kernel.patched` - Patched XNU kernel for QEMU
- `bin/grub-arm64-efi.fd` - GRUB EFI firmware with XNU support
- `bin/qemu-macos-arm64.sh` - QEMU launch script
- `bin/test-xnu-qemu.py` - Testing framework
- `share/grub/grub.cfg` - GRUB configuration

## Usage

1. Run QEMU with the provided script:
   ```bash
   ./bin/qemu-macos-arm64.sh
   ```

2. Run tests:
   ```bash
   ./bin/test-xnu-qemu.py --grub-efi ./bin/grub-arm64-efi.fd --xnu-kernel ./bin/kernel.patched
   ```

## Warning

This is for development and testing purposes only. The security features
have been disabled to enable booting on non-Apple hardware.
EOF
    
    # Create tarball
    cd "${BUILD_DIR}"
    tar -czf "qemu-xnu-arm64-$(date +%Y%m%d).tar.gz" qemu-xnu-arm64-package/
    
    cd "${SCRIPT_DIR}"
    log_success "Package created: ${BUILD_DIR}/qemu-xnu-arm64-$(date +%Y%m%d).tar.gz"
}

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build script for QEMU ARM64 XNU boot system.

Options:
    -h, --help          Show this help message
    --xnu-only          Build only XNU kernel
    --grub-only         Build only GRUB bootloader
    --no-tests          Skip running tests
    --no-package        Skip creating package
    --clean             Clean build directories before building

Environment Variables:
    XNU_SOURCE_DIR      Path to XNU source directory (default: ./xnu)
    GRUB_SOURCE_DIR     Path to GRUB source directory (default: ./grub)
    PARALLEL_JOBS       Number of parallel build jobs (default: nproc)
    BUILD_TYPE          Build type: Release or Debug (default: Release)

Examples:
    # Full build
    $0
    
    # Build only XNU kernel
    $0 --xnu-only
    
    # Clean build
    $0 --clean
    
    # Custom source directories
    XNU_SOURCE_DIR=/path/to/xnu GRUB_SOURCE_DIR=/path/to/grub $0

EOF
}

# Main function
main() {
    local build_xnu=true
    local build_grub=true
    local run_tests_flag=true
    local create_package_flag=true
    local clean_build=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            --xnu-only)
                build_grub=false
                shift
                ;;
            --grub-only)
                build_xnu=false
                shift
                ;;
            --no-tests)
                run_tests_flag=false
                shift
                ;;
            --no-package)
                create_package_flag=false
                shift
                ;;
            --clean)
                clean_build=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting QEMU ARM64 XNU build system"
    log_info "Build directory: ${BUILD_DIR}"
    log_info "Install directory: ${INSTALL_DIR}"
    echo
    
    # Clean if requested
    if [[ "$clean_build" == true ]]; then
        log_info "Cleaning build directories..."
        rm -rf "${BUILD_DIR}" "${INSTALL_DIR}"
    fi
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Setup build environment
    setup_build_environment
    
    # Apply XNU patches
    if [[ "$build_xnu" == true ]]; then
        apply_xnu_patches
    fi
    
    # Build components
    if [[ "$build_xnu" == true ]]; then
        build_xnu_kernel
    fi
    
    if [[ "$build_grub" == true ]]; then
        build_grub_bootloader
    fi
    
    # Create QEMU configuration
    create_qemu_config
    
    # Run tests
    if [[ "$run_tests_flag" == true ]]; then
        run_tests
    fi
    
    # Create package
    if [[ "$create_package_flag" == true ]]; then
        create_package
    fi
    
    log_success "Build completed successfully!"
    log_info "Installation directory: ${INSTALL_DIR}"
    
    if [[ "$create_package_flag" == true ]]; then
        log_info "Package created in: ${BUILD_DIR}"
    fi
}

# Run main function
main "$@"
