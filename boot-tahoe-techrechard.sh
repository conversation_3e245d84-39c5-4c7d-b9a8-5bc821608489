#!/bin/bash
#
# Tahoe TechRechard Boot Script
# 
# This script sets up and boots Tahoe TechRechard ISO using compiled ARM64 GRUB
# with XNU patching support from the untitled folder.

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UNTITLED_FOLDER="${SCRIPT_DIR}/untitled folder"
GRUB_DIR="${SCRIPT_DIR}/grub-arm64"
XNU_PATCHES_DIR="${SCRIPT_DIR}/xnu-generic-arm64-patches"
TAHOE_ISO_NAME="TechRechard.iso"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites for Tahoe TechRechard boot..."
    
    # Check if untitled folder exists
    if [[ ! -d "$UNTITLED_FOLDER" ]]; then
        log_error "Untitled folder not found: $UNTITLED_FOLDER"
        return 1
    fi
    
    # Check for Tahoe ISO
    local tahoe_iso_path="$UNTITLED_FOLDER/$TAHOE_ISO_NAME"
    if [[ ! -f "$tahoe_iso_path" ]]; then
        log_warning "Tahoe ISO not found at: $tahoe_iso_path"
        
        # Search for alternative ISO names
        local found_iso=""
        for iso_name in "Tahoe.iso" "macOS_Tahoe.iso" "macOS-Tahoe-TechRechard.iso" "*.iso"; do
            if [[ -f "$UNTITLED_FOLDER/$iso_name" ]]; then
                found_iso="$iso_name"
                break
            fi
        done
        
        if [[ -n "$found_iso" ]]; then
            log_info "Found alternative ISO: $found_iso"
            TAHOE_ISO_NAME="$found_iso"
        else
            log_error "No Tahoe TechRechard ISO found in untitled folder"
            log_info "Please place the Tahoe TechRechard ISO in: $UNTITLED_FOLDER"
            return 1
        fi
    fi
    
    # Check for compiled ARM64 GRUB
    if [[ ! -d "$GRUB_DIR" ]]; then
        log_error "ARM64 GRUB directory not found: $GRUB_DIR"
        log_info "Please compile ARM64 GRUB first"
        return 1
    fi
    
    # Check for XNU patches
    if [[ ! -d "$XNU_PATCHES_DIR" ]]; then
        log_error "XNU patches directory not found: $XNU_PATCHES_DIR"
        return 1
    fi
    
    log_success "Prerequisites check passed"
    return 0
}

# Function to verify Tahoe ISO
verify_tahoe_iso() {
    local tahoe_iso_path="$UNTITLED_FOLDER/$TAHOE_ISO_NAME"
    
    log_info "Verifying Tahoe TechRechard ISO..."
    
    # Check file size (should be reasonable for macOS ISO)
    local file_size=$(stat -f%z "$tahoe_iso_path" 2>/dev/null || stat -c%s "$tahoe_iso_path" 2>/dev/null)
    if [[ $file_size -lt 1073741824 ]]; then  # Less than 1GB
        log_warning "ISO file seems small: $(($file_size / 1024 / 1024)) MB"
    fi
    
    # Check if it's a valid ISO
    if command -v file >/dev/null 2>&1; then
        local file_type=$(file "$tahoe_iso_path")
        if [[ "$file_type" == *"ISO 9660"* ]] || [[ "$file_type" == *"UDF"* ]]; then
            log_success "Valid ISO format detected"
        else
            log_warning "File may not be a valid ISO: $file_type"
        fi
    fi
    
    # Try to mount and check contents (macOS/Linux)
    if command -v hdiutil >/dev/null 2>&1; then
        log_info "Checking ISO contents with hdiutil..."
        local mount_point=$(mktemp -d)
        
        if hdiutil attach "$tahoe_iso_path" -mountpoint "$mount_point" -readonly >/dev/null 2>&1; then
            # Check for macOS files
            if [[ -f "$mount_point/System/Library/CoreServices/boot.efi" ]]; then
                log_success "Found macOS boot.efi in ISO"
            elif [[ -f "$mount_point/System/Library/Kernels/kernel" ]]; then
                log_success "Found XNU kernel in ISO"
            else
                log_warning "macOS boot files not found in expected locations"
            fi
            
            # Unmount
            hdiutil detach "$mount_point" >/dev/null 2>&1
            rmdir "$mount_point"
        else
            log_warning "Could not mount ISO for verification"
        fi
    fi
    
    log_success "ISO verification complete"
}

# Function to setup GRUB configuration
setup_grub_config() {
    log_info "Setting up GRUB configuration for Tahoe TechRechard..."
    
    local grub_cfg_dir="$GRUB_DIR/grub"
    mkdir -p "$grub_cfg_dir"
    
    # Copy our Tahoe GRUB configuration
    cp "$SCRIPT_DIR/grub-tahoe-techrechard.cfg" "$grub_cfg_dir/grub.cfg"
    
    # Update paths in GRUB config to point to correct untitled folder
    local untitled_path=$(realpath "$UNTITLED_FOLDER")
    sed -i.bak "s|/untitled/|$untitled_path/|g" "$grub_cfg_dir/grub.cfg"
    
    log_success "GRUB configuration updated"
}

# Function to create bootable USB (if needed)
create_bootable_usb() {
    local usb_device="$1"
    
    if [[ -z "$usb_device" ]]; then
        log_info "No USB device specified, skipping USB creation"
        return 0
    fi
    
    log_warning "Creating bootable USB will ERASE all data on $usb_device"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "USB creation cancelled"
        return 0
    fi
    
    log_info "Creating bootable USB for Tahoe TechRechard..."
    
    # Format USB as FAT32
    if command -v diskutil >/dev/null 2>&1; then
        # macOS
        diskutil eraseDisk FAT32 "TAHOE_BOOT" "$usb_device"
        local usb_mount="/Volumes/TAHOE_BOOT"
    else
        # Linux
        sudo mkfs.fat -F32 "$usb_device"
        local usb_mount="/mnt/tahoe_boot"
        sudo mkdir -p "$usb_mount"
        sudo mount "$usb_device" "$usb_mount"
    fi
    
    # Copy GRUB files
    cp -r "$GRUB_DIR"/* "$usb_mount/"
    
    # Copy Tahoe ISO
    cp "$UNTITLED_FOLDER/$TAHOE_ISO_NAME" "$usb_mount/untitled/"
    
    # Sync and unmount
    sync
    if command -v diskutil >/dev/null 2>&1; then
        diskutil unmount "$usb_mount"
    else
        sudo umount "$usb_mount"
    fi
    
    log_success "Bootable USB created successfully"
}

# Function to start QEMU boot (for testing)
start_qemu_boot() {
    log_info "Starting QEMU boot test for Tahoe TechRechard..."
    
    # Check if QEMU is available
    if ! command -v qemu-system-aarch64 >/dev/null 2>&1; then
        log_error "QEMU ARM64 not found. Please install qemu-system-aarch64"
        return 1
    fi
    
    # QEMU parameters for ARM64
    local qemu_args=(
        -M virt
        -cpu cortex-a72
        -m 4G
        -smp 4
        -bios "$GRUB_DIR/grub-efi-arm64.efi"
        -drive "file=$UNTITLED_FOLDER/$TAHOE_ISO_NAME,media=cdrom,readonly=on"
        -netdev user,id=net0
        -device virtio-net-pci,netdev=net0
        -device virtio-gpu-pci
        -device qemu-xhci
        -device usb-kbd
        -device usb-mouse
        -monitor stdio
        -serial file:tahoe-boot.log
    )
    
    log_info "Starting QEMU with Tahoe TechRechard ISO..."
    log_info "QEMU log will be saved to: tahoe-boot.log"
    
    qemu-system-aarch64 "${qemu_args[@]}"
}

# Function to display boot instructions
display_boot_instructions() {
    log_info "Tahoe TechRechard Boot Instructions"
    echo
    echo "Your Tahoe TechRechard boot environment is ready!"
    echo
    echo "Boot Options:"
    echo "1. QEMU Testing: Run './boot-tahoe-techrechard.sh --qemu'"
    echo "2. USB Boot: Run './boot-tahoe-techrechard.sh --usb /dev/sdX'"
    echo "3. Direct Boot: Use your compiled ARM64 GRUB with the generated config"
    echo
    echo "Files Created:"
    echo "- GRUB Config: $GRUB_DIR/grub/grub.cfg"
    echo "- Tahoe ISO: $UNTITLED_FOLDER/$TAHOE_ISO_NAME"
    echo "- Boot Log: tahoe-boot.log (when using QEMU)"
    echo
    echo "GRUB Menu Options:"
    echo "- macOS Tahoe TechRechard (Generic ARM64) - Main boot option"
    echo "- macOS Tahoe TechRechard (EFI Boot) - Alternative EFI method"
    echo "- macOS Tahoe TechRechard (Recovery Mode) - Recovery boot"
    echo "- macOS Tahoe TechRechard (Verbose Debug) - Debug mode"
    echo "- macOS Tahoe TechRechard (Safe Mode) - Safe boot"
    echo
    echo "Boot Arguments Applied:"
    echo "- generic_arm64_mode=1"
    echo "- disable_amfi=1 disable_sep=1 disable_ppl=1"
    echo "- cs_enforcement_disable=1"
    echo "- tahoe_mode=1 techrechard_compat=1"
    echo "- spoof_board_id=J313AP spoof_chip_id=T8103"
    echo
    log_warning "Remember: All Apple security features are disabled!"
}

# Main function
main() {
    local action=""
    local usb_device=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --qemu)
                action="qemu"
                shift
                ;;
            --usb)
                action="usb"
                usb_device="$2"
                shift 2
                ;;
            --help|-h)
                echo "Usage: $0 [--qemu] [--usb /dev/sdX] [--help]"
                echo
                echo "Options:"
                echo "  --qemu          Start QEMU boot test"
                echo "  --usb DEVICE    Create bootable USB"
                echo "  --help          Show this help"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "Starting Tahoe TechRechard boot setup"
    echo
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Verify ISO
    verify_tahoe_iso
    
    # Setup GRUB configuration
    setup_grub_config
    
    # Execute requested action
    case "$action" in
        "qemu")
            start_qemu_boot
            ;;
        "usb")
            create_bootable_usb "$usb_device"
            ;;
        *)
            display_boot_instructions
            ;;
    esac
    
    log_success "Tahoe TechRechard boot setup complete!"
}

# Run main function
main "$@"
