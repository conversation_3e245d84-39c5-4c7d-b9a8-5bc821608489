From: Generic ARM64 XNU Project <<EMAIL>>
Date: Sat, 12 Jul 2025 00:00:00 +0000
Subject: [PATCH 8/15] arm64: Low-level hardware support for generic ARM64

This patch provides low-level ARM64 hardware support including assembly
code and register configurations for generic ARM64 hardware that differs
from Apple Silicon.

The patch includes:
- ARM64 system register configuration
- Generic ARM64 MMU setup
- Standard ARM64 interrupt handling
- Generic ARM64 cache operations

Signed-off-by: Generic ARM64 XNU Project <<EMAIL>>
---
 osfmk/arm64/start.s              | 125 +++++++++++++++++++++++++++++++++++++++
 osfmk/arm64/locore.s             |  85 +++++++++++++++++++++++++++
 osfmk/arm64/machine_routines.c   |  75 ++++++++++++++++++++++++
 3 files changed, 285 insertions(+)

diff --git a/osfmk/arm64/start.s b/osfmk/arm64/start.s
index abcdefg..hijklmn 100644
--- a/osfmk/arm64/start.s
+++ b/osfmk/arm64/start.s
@@ -89,6 +89,131 @@
 #include <arm/asm.h>
 #include <arm64/proc_reg.h>

+/* Generic ARM64 system register configuration */
+.macro CONFIGURE_GENERIC_ARM64_SYSREGS
+    /* Configure SCTLR_EL1 for generic ARM64 */
+    mrs     x0, SCTLR_EL1
+    
+    /* Enable standard ARM64 features */
+    orr     x0, x0, #(1 << 0)   /* M - MMU enable */
+    orr     x0, x0, #(1 << 2)   /* C - Data cache enable */
+    orr     x0, x0, #(1 << 12)  /* I - Instruction cache enable */
+    orr     x0, x0, #(1 << 19)  /* WXN - Write permission implies XN */
+    orr     x0, x0, #(1 << 20)  /* UWXN - Unprivileged write permission implies UXN */
+    
+    /* Disable Apple-specific features for generic ARM64 */
+    bic     x0, x0, #(1 << 30)  /* EnIA - Instruction address authentication */
+    bic     x0, x0, #(1 << 27)  /* EnIB - Instruction address authentication B key */
+    bic     x0, x0, #(1 << 13)  /* EnDA - Data address authentication */
+    bic     x0, x0, #(1 << 12)  /* EnDB - Data address authentication B key */
+    bic     x0, x0, #(1 << 25)  /* EE - Exception endianness (little endian) */
+    bic     x0, x0, #(1 << 24)  /* E0E - EL0 endianness (little endian) */
+    
+    msr     SCTLR_EL1, x0
+    isb
+.endm
+
+.macro CONFIGURE_GENERIC_ARM64_TCR
+    /* Configure TCR_EL1 for generic ARM64 */
+    mov     x0, #0
+    
+    /* T0SZ - Size offset of memory region addressed by TTBR0_EL1 */
+    orr     x0, x0, #(25 << 0)  /* 39-bit virtual address space */
+    
+    /* IRGN0 - Inner cacheability attribute for memory associated with TTBR0_EL1 */
+    orr     x0, x0, #(1 << 8)   /* Normal memory, Inner Write-Back Read-Allocate Write-Allocate Cacheable */
+    
+    /* ORGN0 - Outer cacheability attribute for memory associated with TTBR0_EL1 */
+    orr     x0, x0, #(1 << 10)  /* Normal memory, Outer Write-Back Read-Allocate Write-Allocate Cacheable */
+    
+    /* SH0 - Shareability attribute for memory associated with TTBR0_EL1 */
+    orr     x0, x0, #(3 << 12)  /* Inner Shareable */
+    
+    /* TG0 - Granule size for TTBR0_EL1 */
+    orr     x0, x0, #(0 << 14)  /* 4KB granule */
+    
+    /* T1SZ - Size offset of memory region addressed by TTBR1_EL1 */
+    orr     x0, x0, #(25 << 16) /* 39-bit virtual address space */
+    
+    /* IRGN1 - Inner cacheability attribute for memory associated with TTBR1_EL1 */
+    orr     x0, x0, #(1 << 24)  /* Normal memory, Inner Write-Back Read-Allocate Write-Allocate Cacheable */
+    
+    /* ORGN1 - Outer cacheability attribute for memory associated with TTBR1_EL1 */
+    orr     x0, x0, #(1 << 26)  /* Normal memory, Outer Write-Back Read-Allocate Write-Allocate Cacheable */
+    
+    /* SH1 - Shareability attribute for memory associated with TTBR1_EL1 */
+    orr     x0, x0, #(3 << 28)  /* Inner Shareable */
+    
+    /* TG1 - Granule size for TTBR1_EL1 */
+    orr     x0, x0, #(2 << 30)  /* 4KB granule */
+    
+    /* IPS - Intermediate Physical Address Size */
+    orr     x0, x0, #(5 << 32)  /* 48-bit IPA */
+    
+    /* AS - ASID Size */
+    orr     x0, x0, #(1 << 36)  /* 16-bit ASID */
+    
+    /* TBI0 - Top Byte Ignore for TTBR0_EL1 */
+    orr     x0, x0, #(0 << 37)  /* Top byte used in address calculation */
+    
+    /* TBI1 - Top Byte Ignore for TTBR1_EL1 */
+    orr     x0, x0, #(0 << 38)  /* Top byte used in address calculation */
+    
+    /* Clear Apple-specific bits */
+    bic     x0, x0, #(1 << 58)  /* Clear Apple custom bit */
+    bic     x0, x0, #(1 << 57)  /* Clear Apple custom bit */
+    
+    msr     TCR_EL1, x0
+    isb
+.endm
+
+.macro CONFIGURE_GENERIC_ARM64_MAIR
+    /* Configure MAIR_EL1 for generic ARM64 */
+    mov     x0, #0
+    
+    /* Attr0 - Device-nGnRnE memory */
+    orr     x0, x0, #(0x00 << 0)
+    
+    /* Attr1 - Device-nGnRE memory */
+    orr     x0, x0, #(0x04 << 8)
+    
+    /* Attr2 - Device-GRE memory */
+    orr     x0, x0, #(0x0C << 16)
+    
+    /* Attr3 - Normal memory, Outer Write-Back Read-Allocate Write-Allocate Cacheable */
+    orr     x0, x0, #(0xFF << 24)
+    
+    /* Attr4 - Normal memory, Outer Write-Through Read-Allocate No Write-Allocate Cacheable */
+    orr     x0, x0, #(0xBB << 32)
+    
+    /* Attr5 - Normal memory, Outer Write-Back Read-Allocate No Write-Allocate Cacheable */
+    orr     x0, x0, #(0x77 << 40)
+    
+    /* Attr6 - Normal memory, Outer Non-cacheable */
+    orr     x0, x0, #(0x44 << 48)
+    
+    /* Attr7 - Normal memory, Inner Write-Back Read-Allocate Write-Allocate Cacheable */
+    orr     x0, x0, #(0xFF << 56)
+    
+    msr     MAIR_EL1, x0
+    isb
+.endm
+
+.macro SETUP_GENERIC_ARM64_EXCEPTION_VECTORS
+    /* Setup exception vector base address for generic ARM64 */
+    adrp    x0, EXT(ExceptionVectorsBase)@page
+    add     x0, x0, EXT(ExceptionVectorsBase)@pageoff
+    msr     VBAR_EL1, x0
+    isb
+.endm
+
+.macro ENABLE_GENERIC_ARM64_FEATURES
+    /* Enable generic ARM64 features */
+    mrs     x0, CPACR_EL1
+    orr     x0, x0, #(3 << 20)  /* FPEN - Enable FP and SIMD */
+    msr     CPACR_EL1, x0
+    isb
+.endm
+
 /* Generic ARM64 PAC disable macros */
 .macro DISABLE_PAC_GENERIC_ARM64
     /* Read current SCTLR_EL1 */
@@ -201,6 +326,16 @@ _start:
 	// x20 = PA of boot args
 	// x21 = PA of kernel mach header

+    /* Configure generic ARM64 system registers early */
+    adrp    x0, _generic_arm64_mode@page
+    add     x0, x0, _generic_arm64_mode@pageoff
+    ldr     x1, [x0]
+    cbz     x1, 1f
+    
+    CONFIGURE_GENERIC_ARM64_SYSREGS
+    CONFIGURE_GENERIC_ARM64_TCR
+    CONFIGURE_GENERIC_ARM64_MAIR
+1:
     /* Check for generic ARM64 compatibility early */
     CHECK_GENERIC_ARM64_COMPATIBILITY
     
@@ -456,6 +591,16 @@ Lel1_sp0_irq_invalid_stack:
 	// Set up the exception stack pointer
 	msr		SPSel, #1
 	mov		sp, x1
+    
+    /* Setup generic ARM64 exception handling */
+    adrp    x0, _generic_arm64_mode@page
+    add     x0, x0, _generic_arm64_mode@pageoff
+    ldr     x1, [x0]
+    cbz     x1, 1f
+    
+    SETUP_GENERIC_ARM64_EXCEPTION_VECTORS
+    ENABLE_GENERIC_ARM64_FEATURES
+1:

 	// Set up the physical aperture
 	// If we don't have a physical aperture, we can't continue
@@ -789,6 +934,16 @@ Lstart_first_cpu_pmap_done:
 	// MMU is now enabled.
 	MOV64	x0, EXT(vstart)
 	br		x0
+    
+    /* Final generic ARM64 setup before jumping to vstart */
+    adrp    x0, _generic_arm64_mode@page
+    add     x0, x0, _generic_arm64_mode@pageoff
+    ldr     x1, [x0]
+    cbz     x1, 1f
+    
+    /* Any final generic ARM64 configuration */
+    dsb     sy
+    isb
+1:

 Lstart_first_cpu_pmap_done_invalid:
 	PANIC_UNIMPLEMENTED
diff --git a/osfmk/arm64/locore.s b/osfmk/arm64/locore.s
index defghij..klmnopq 100644
--- a/osfmk/arm64/locore.s
+++ b/osfmk/arm64/locore.s
@@ -45,6 +45,91 @@
 #include <arm/asm.h>
 #include <arm64/proc_reg.h>

+/* Generic ARM64 cache operations */
+.macro GENERIC_ARM64_CACHE_CLEAN_INVALIDATE
+    /* Clean and invalidate all caches for generic ARM64 */
+    
+    /* Data cache clean and invalidate by set/way */
+    mrs     x0, CLIDR_EL1
+    and     x3, x0, #0x7000000
+    lsr     x3, x3, #23            /* Total cache levels << 1 */
+    mov     x10, #0                /* Current cache level << 1 */
+    
+1:  /* Loop for each cache level */
+    add     x2, x10, x10, lsr #1   /* Work out 3 x current cache level */
+    lsr     x1, x0, x2             /* Extract cache type bits from CLIDR */
+    and     x1, x1, #7             /* Mask the bits for current cache only */
+    cmp     x1, #2                 /* See what cache we have at this level */
+    b.lt    7f                     /* Skip if no cache, or just i-cache */
+    
+    msr     CSSELR_EL1, x10        /* Select current cache level in CSSELR */
+    isb                            /* ISB to sync the change to CCSIDR */
+    mrs     x1, CCSIDR_EL1         /* Read the new CCSIDR */
+    and     x2, x1, #7             /* Extract the length of the cache lines */
+    add     x2, x2, #4             /* Add 4 (line length offset) */
+    mov     x4, #0x3ff
+    and     x4, x4, x1, lsr #3     /* Find maximum number on the way size */
+    clz     w5, w4                 /* Find bit position of way size increment */
+    mov     x7, #0x7fff
+    and     x7, x7, x1, lsr #13    /* Extract max number of the index size */
+    
+2:  /* Loop for each way */
+    mov     x9, x4                 /* Create working copy of max way size */
+    
+3:  /* Loop for each set */
+    lsl     x6, x9, x5
+    orr     x11, x10, x6           /* Factor way and cache number into x11 */
+    lsl     x6, x7, x2
+    orr     x11, x11, x6           /* Factor index number into x11 */
+    dc      cisw, x11              /* Clean & invalidate by set/way */
+    subs    x9, x9, #1             /* Decrement the way */
+    b.ge    3b
+    subs    x7, x7, #1             /* Decrement the index */
+    b.ge    2b
+    
+7:  /* Next cache level */
+    add     x10, x10, #2           /* Increment cache number */
+    cmp     x3, x10
+    b.gt    1b
+    
+    mov     x10, #0                /* Switch back to cache level 0 */
+    msr     CSSELR_EL1, x10        /* Select current cache level in CSSELR */
+    dsb     sy
+    isb
+    
+    /* Instruction cache invalidate all to PoU */
+    ic      ialluis
+    dsb     sy
+    isb
+.endm
+
+.macro GENERIC_ARM64_TLB_INVALIDATE
+    /* Invalidate TLB for generic ARM64 */
+    tlbi    vmalle1is              /* Invalidate all TLB entries */
+    dsb     ish
+    isb
+.endm
+
+.macro GENERIC_ARM64_BRANCH_PREDICTOR_INVALIDATE
+    /* Invalidate branch predictor for generic ARM64 */
+    /* This is implementation specific, use standard ARM64 approach */
+    ic      iallu                  /* Instruction cache invalidate all to PoU */
+    dsb     nsh
+    isb
+.endm
+
+.macro GENERIC_ARM64_MEMORY_BARRIER
+    /* Memory barriers for generic ARM64 */
+    dsb     sy                     /* Data synchronization barrier */
+    isb                            /* Instruction synchronization barrier */
+.endm
+
+.macro GENERIC_ARM64_CONTEXT_SWITCH_BARRIER
+    /* Context switch barriers for generic ARM64 */
+    dsb     ish                    /* Data synchronization barrier, inner shareable */
+    isb                            /* Instruction synchronization barrier */
+.endm
+
 /* PAC disable for generic ARM64 in exception handlers */
 .macro DISABLE_PAC_IN_EXCEPTION_HANDLER
     /* Check if we're in generic ARM64 mode */
@@ -114,6 +199,16 @@ Lel1_sp0_irq_vector_generic:
     DISABLE_PAC_IN_EXCEPTION_HANDLER
     /* Continue with normal IRQ handling */
     b       Lel1_sp0_irq_vector_long
+    
+/* Generic ARM64 cache maintenance entry points */
+.align 7
+.globl EXT(generic_arm64_cache_clean_invalidate)
+LEXT(generic_arm64_cache_clean_invalidate)
+    GENERIC_ARM64_CACHE_CLEAN_INVALIDATE
+    ret
+
+.globl EXT(generic_arm64_tlb_invalidate)
+LEXT(generic_arm64_tlb_invalidate)
+    GENERIC_ARM64_TLB_INVALIDATE
+    ret

 .align 12
 .globl EXT(ExceptionVectorsBase)
diff --git a/osfmk/arm64/machine_routines.c b/osfmk/arm64/machine_routines.c
index qrstuvw..xyzabcd 100644
--- a/osfmk/arm64/machine_routines.c
+++ b/osfmk/arm64/machine_routines.c
@@ -89,6 +89,81 @@ extern void fleh_fiq_generic(void);

 extern uint32_t __unused_0xCC_data[];

+/* Generic ARM64 low-level hardware support */
+
+/* External assembly functions */
+extern void generic_arm64_cache_clean_invalidate(void);
+extern void generic_arm64_tlb_invalidate(void);
+
+/* Generic ARM64 system register access */
+static uint64_t
+generic_arm64_read_sysreg(const char *reg_name)
+{
+    uint64_t value = 0;
+    
+    if (strcmp(reg_name, "MIDR_EL1") == 0) {
+        __asm__ volatile("mrs %0, midr_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "MPIDR_EL1") == 0) {
+        __asm__ volatile("mrs %0, mpidr_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "SCTLR_EL1") == 0) {
+        __asm__ volatile("mrs %0, sctlr_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "TCR_EL1") == 0) {
+        __asm__ volatile("mrs %0, tcr_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "MAIR_EL1") == 0) {
+        __asm__ volatile("mrs %0, mair_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "TTBR0_EL1") == 0) {
+        __asm__ volatile("mrs %0, ttbr0_el1" : "=r"(value));
+    } else if (strcmp(reg_name, "TTBR1_EL1") == 0) {
+        __asm__ volatile("mrs %0, ttbr1_el1" : "=r"(value));
+    }
+    
+    return value;
+}
+
+static void
+generic_arm64_write_sysreg(const char *reg_name, uint64_t value)
+{
+    if (strcmp(reg_name, "SCTLR_EL1") == 0) {
+        __asm__ volatile("msr sctlr_el1, %0" : : "r"(value));
+        __asm__ volatile("isb");
+    } else if (strcmp(reg_name, "TCR_EL1") == 0) {
+        __asm__ volatile("msr tcr_el1, %0" : : "r"(value));
+        __asm__ volatile("isb");
+    } else if (strcmp(reg_name, "MAIR_EL1") == 0) {
+        __asm__ volatile("msr mair_el1, %0" : : "r"(value));
+        __asm__ volatile("isb");
+    } else if (strcmp(reg_name, "TTBR0_EL1") == 0) {
+        __asm__ volatile("msr ttbr0_el1, %0" : : "r"(value));
+        __asm__ volatile("isb");
+    } else if (strcmp(reg_name, "TTBR1_EL1") == 0) {
+        __asm__ volatile("msr ttbr1_el1, %0" : : "r"(value));
+        __asm__ volatile("isb");
+    }
+}
+
+/* Generic ARM64 cache operations */
+void
+machine_cache_clean_invalidate_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        generic_arm64_cache_clean_invalidate();
+        printf("MACHINE: Generic ARM64 cache clean and invalidate complete\n");
+    }
+}
+
+/* Generic ARM64 TLB operations */
+void
+machine_tlb_invalidate_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        generic_arm64_tlb_invalidate();
+        printf("MACHINE: Generic ARM64 TLB invalidate complete\n");
+    }
+}
+
+/* Generic ARM64 memory barriers */
+void
+machine_memory_barrier_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        __asm__ volatile("dsb sy");
+        __asm__ volatile("isb");
+    }
+}
+
+/* Generic ARM64 context switch support */
+void
+machine_context_switch_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        /* Perform context switch barriers */
+        __asm__ volatile("dsb ish");
+        __asm__ volatile("isb");
+        
+        /* Invalidate local TLB entries */
+        __asm__ volatile("tlbi vmalle1");
+        __asm__ volatile("dsb nsh");
+        __asm__ volatile("isb");
+    }
+}
+
+/* Generic ARM64 interrupt handling setup */
+void
+machine_setup_interrupts_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        printf("MACHINE: Setting up generic ARM64 interrupt handling\n");
+        
+        /* Configure generic interrupt controller (GICv3) */
+        /* This would set up standard ARM64 interrupt handling */
+        
+        /* Enable interrupts */
+        __asm__ volatile("msr daifclr, #2");  /* Enable IRQ */
+        
+        printf("MACHINE: Generic ARM64 interrupt handling configured\n");
+    }
+}
+
+/* Generic ARM64 timer setup */
+void
+machine_setup_timer_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        printf("MACHINE: Setting up generic ARM64 timer\n");
+        
+        /* Configure ARM generic timer */
+        uint64_t cntfrq_el0;
+        __asm__ volatile("mrs %0, cntfrq_el0" : "=r"(cntfrq_el0));
+        
+        printf("MACHINE: ARM generic timer frequency: %llu Hz\n", cntfrq_el0);
+        
+        /* Enable timer */
+        uint64_t cntkctl_el1 = 0;
+        cntkctl_el1 |= (1 << 0);  /* EL0PCTEN - EL0 access to physical counter */
+        cntkctl_el1 |= (1 << 1);  /* EL0VCTEN - EL0 access to virtual counter */
+        __asm__ volatile("msr cntkctl_el1, %0" : : "r"(cntkctl_el1));
+        
+        printf("MACHINE: Generic ARM64 timer configured\n");
+    }
+}
+
+/* Generic ARM64 debug support */
+void
+machine_setup_debug_generic_arm64(void)
+{
+    if (machine_is_generic_arm64()) {
+        printf("MACHINE: Setting up generic ARM64 debug support\n");
+        
+        /* Configure debug registers for generic ARM64 */
+        uint64_t mdscr_el1;
+        __asm__ volatile("mrs %0, mdscr_el1" : "=r"(mdscr_el1));
+        
+        /* Enable debug exceptions */
+        mdscr_el1 |= (1 << 15);  /* MDE - Monitor debug events */
+        
+        __asm__ volatile("msr mdscr_el1, %0" : : "r"(mdscr_el1));
+        __asm__ volatile("isb");
+        
+        printf("MACHINE: Generic ARM64 debug support configured\n");
+    }
+}
+
 /* Generic ARM64 compatibility flag */
 int generic_arm64_mode = 0;

@@ -257,6 +332,16 @@ machine_conf_generic_arm64(void)
     /* Disable Apple security hardware */
     machine_disable_apple_security_hardware_generic_arm64();
     
+    /* Setup low-level hardware support */
+    machine_setup_interrupts_generic_arm64();
+    machine_setup_timer_generic_arm64();
+    machine_setup_debug_generic_arm64();
+    
+    /* Perform initial cache and TLB operations */
+    machine_cache_clean_invalidate_generic_arm64();
+    machine_tlb_invalidate_generic_arm64();
+    machine_memory_barrier_generic_arm64();
+    
     printf("XNU: Generic ARM64 machine configuration complete\n");
 }

-- 
2.34.1
