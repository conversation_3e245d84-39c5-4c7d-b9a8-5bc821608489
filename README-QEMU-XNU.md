# QEMU ARM64 XNU Bootloader Project

This project enables booting macOS XNU kernel on QEMU ARM64 virtualization by providing:

1. **XNU Kernel Patches** - Modifications to support standard ARM64 architecture
2. **GRUB ARM64 XNU Loader** - Modified bootloader with QEMU compatibility
3. **Secure Boot Bypass** - Patches to disable Apple-specific security features
4. **QEMU Configuration** - Complete virtualization setup and testing framework

## ⚠️ Important Disclaimer

**This project is for educational, development, and testing purposes only.**

- Disables critical security features of macOS
- Should only be used in controlled environments
- Not suitable for production use
- May violate software licenses - check your local laws
- Requires legitimate macOS installation and kernel

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   QEMU ARM64    │    │  GRUB Modified  │    │  XNU Patched    │
│   Virtualization│ -> │   Bootloader    │ -> │    Kernel       │
│                 │    │                 │    │                 │
│ • Standard ARM64│    │ • QEMU Detection│    │ • No Apple deps │
│ • EFI Firmware  │    │ • FDT Support   │    │ • Security bypass│
│ • Device Tree   │    │ • Memory Layout │    │ • QEMU compat   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

### System Requirements

- Linux host system (Ubuntu 20.04+ recommended)
- 16GB+ RAM (8GB+ for QEMU VM)
- 100GB+ free disk space
- ARM64 cross-compilation tools

### Required Software

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y \
    build-essential \
    git \
    clang \
    gcc-aarch64-linux-gnu \
    qemu-system-arm \
    qemu-efi-aarch64 \
    python3 \
    python3-pip \
    autotools-dev \
    autoconf \
    automake \
    bison \
    flex \
    gettext

# Additional tools
pip3 install pexpect
```

### Source Code

You'll need:
- XNU kernel source code (from Apple Open Source)
- GRUB source code (GNU GRUB)
- This project's patches and modifications

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone this project
git clone <this-repo-url> qemu-xnu-arm64
cd qemu-xnu-arm64

# Clone required sources
git clone https://github.com/apple/darwin-xnu.git xnu
git clone https://git.savannah.gnu.org/git/grub.git grub

# Make build script executable
chmod +x build-xnu-qemu.sh
```

### 2. Build Everything

```bash
# Full build (this will take 30-60 minutes)
./build-xnu-qemu.sh

# Or build components separately
./build-xnu-qemu.sh --xnu-only      # Build only XNU kernel
./build-xnu-qemu.sh --grub-only     # Build only GRUB bootloader
```

### 3. Run QEMU

```bash
# Launch QEMU with macOS XNU
cd install
./bin/qemu-macos-arm64.sh

# Or with custom options
./bin/qemu-macos-arm64.sh --memory 8G --cores 8 --debug
```

### 4. Test the System

```bash
# Run automated tests
./bin/test-xnu-qemu.py \
    --grub-efi ./bin/grub-arm64-efi.fd \
    --xnu-kernel ./bin/kernel.patched
```

## 📁 Project Structure

```
qemu-xnu-arm64/
├── README-QEMU-XNU.md              # This file
├── build-xnu-qemu.sh               # Main build script
├── xnu-patches/                    # XNU kernel patches
│   ├── 0001-arm64-remove-apple-silicon-dependencies.patch
│   ├── 0002-devicetree-qemu-compatibility.patch
│   ├── 0003-memory-management-qemu.patch
│   ├── 0004-secure-boot-bypass.patch
│   └── 0005-disable-apple-security-features.patch
├── grub/                           # Modified GRUB source
│   └── grub-core/loader/arm64/     # ARM64 XNU loader
├── qemu-config/                    # QEMU configuration
│   ├── qemu-macos-arm64.sh         # QEMU launch script
│   └── test-xnu-qemu.py            # Testing framework
├── build/                          # Build artifacts
└── install/                        # Installation directory
    ├── bin/                        # Executables
    │   ├── kernel.patched          # Patched XNU kernel
    │   ├── grub-arm64-efi.fd       # GRUB EFI firmware
    │   ├── qemu-macos-arm64.sh     # QEMU launcher
    │   └── test-xnu-qemu.py        # Test script
    └── share/grub/grub.cfg         # GRUB configuration
```

## 🔧 Detailed Build Process

### XNU Kernel Patches

The XNU kernel requires several patches for QEMU compatibility:

1. **Remove Apple Silicon Dependencies** (`0001-*`)
   - Disables Apple-specific hardware detection
   - Adds QEMU environment detection
   - Removes Apple Silicon CPU features

2. **Device Tree Compatibility** (`0002-*`)
   - Adds FDT (Flattened Device Tree) support
   - Converts between Apple ADT and standard FDT
   - QEMU hardware detection

3. **Memory Management** (`0003-*`)
   - Adapts to QEMU's flexible memory layout
   - Removes Apple's fixed memory regions
   - Standard ARM64 memory management

4. **Secure Boot Bypass** (`0004-*`)
   - Disables Apple's secure boot verification
   - Bypasses IMG4 verification
   - Removes code signing requirements

5. **Security Features Disable** (`0005-*`)
   - Disables pointer authentication
   - Removes Apple-specific security features
   - Bypasses MAC framework checks

### GRUB Modifications

The GRUB bootloader is extended with:

- **QEMU Detection** - Automatically detects QEMU environment
- **FDT Support** - Creates standard device trees instead of Apple ADT
- **Memory Layout** - Adapts to QEMU's memory configuration
- **Cache Operations** - Standard ARM64 cache management
- **Boot Arguments** - QEMU-compatible boot parameter passing

### Build Configuration

```bash
# XNU build configuration
export ARCH=arm64
export PLATFORM=QEMU
export TARGET_CONFIGS="QEMU ARM64"

# GRUB build configuration
./configure \
    --target=aarch64-linux-gnu \
    --with-platform=efi \
    --enable-arm64-xnu \
    --disable-werror
```

## 🧪 Testing and Validation

### Automated Testing

The testing framework validates:

- GRUB bootloader functionality
- XNU module loading
- QEMU environment detection
- Kernel boot process
- Security bypass operation

```bash
# Run full test suite
./bin/test-xnu-qemu.py --grub-efi ./bin/grub-arm64-efi.fd

# Test specific components
python3 -c "
from test_xnu_qemu import QEMUXNUTester
tester = QEMUXNUTester(config)
tester.test_grub_boot()
"
```

### Manual Testing

1. **GRUB Boot Test**
   ```bash
   # Launch QEMU and verify GRUB menu appears
   ./bin/qemu-macos-arm64.sh
   # Look for "GNU GRUB" and XNU menu entries
   ```

2. **Kernel Loading Test**
   ```bash
   # In GRUB menu, select "macOS XNU (QEMU ARM64)"
   # Verify kernel loading messages
   ```

3. **Debug Mode**
   ```bash
   # Launch with debug enabled
   DEBUG=1 ./bin/qemu-macos-arm64.sh
   # Connect GDB to localhost:1234
   ```

## 🐛 Troubleshooting

### Common Issues

1. **GRUB Doesn't Start**
   ```bash
   # Check EFI firmware
   ls -la install/bin/grub-arm64-efi.fd
   
   # Verify QEMU ARM64 support
   qemu-system-aarch64 --version
   ```

2. **XNU Module Not Found**
   ```bash
   # Rebuild GRUB with XNU support
   ./build-xnu-qemu.sh --grub-only
   
   # Check module installation
   ls install/lib/grub/arm64-efi/xnu_arm64.mod
   ```

3. **Kernel Panic on Boot**
   ```bash
   # Check kernel patches applied
   grep -r "qemu_is_secure_boot_bypassed" xnu/
   
   # Use safe mode
   # In GRUB: "macOS XNU (QEMU ARM64) - Safe Mode"
   ```

4. **Memory Allocation Errors**
   ```bash
   # Increase QEMU memory
   MEMORY_SIZE=8G ./bin/qemu-macos-arm64.sh
   
   # Check memory layout
   grep "memory layout" test-logs/qemu-serial.log
   ```

### Debug Information

Enable verbose logging:

```bash
# QEMU debug
DEBUG=1 VERBOSE=1 ./bin/qemu-macos-arm64.sh

# GRUB debug
# Add to grub.cfg:
set debug=all

# XNU debug
# Boot with: debug=0xfff kextlog=0xfff
```

### Log Files

- `test-logs/qemu-serial.log` - QEMU serial console output
- `test-logs/qemu-debug.log` - QEMU debug information
- `test-results/test-report.json` - Automated test results

## 📚 Technical Details

### Memory Layout

```
QEMU ARM64 Virtual Machine Memory Layout:
0x00000000 - 0x07FFFFFF : Flash/ROM (128MB)
0x08000000 - 0x0801FFFF : GIC Distributor (128KB)
0x08010000 - 0x0801FFFF : GIC CPU Interface (64KB)
0x09000000 - 0x09000FFF : UART (4KB)
0x40000000 - 0xBFFFFFFF : Main Memory (2GB default)
```

### Boot Process

1. **QEMU Startup**
   - Loads GRUB EFI firmware
   - Initializes ARM64 virtual machine
   - Sets up device tree

2. **GRUB Initialization**
   - Detects QEMU environment
   - Loads XNU ARM64 module
   - Presents boot menu

3. **XNU Loading**
   - Parses Mach-O kernel
   - Sets up QEMU memory layout
   - Creates standard device tree
   - Bypasses security checks

4. **Kernel Boot**
   - Transfers control to XNU
   - XNU detects QEMU mode
   - Disables Apple-specific features
   - Continues with standard ARM64 boot

### Security Implications

**WARNING**: This project disables critical security features:

- Code signing verification
- Secure boot chain
- Memory protection features
- Apple's MAC framework
- Hardware security features

**Use only in isolated, controlled environments.**

## 🤝 Contributing

This project is for educational purposes. If you want to contribute:

1. Fork the repository
2. Create a feature branch
3. Test thoroughly
4. Submit a pull request

Please ensure all contributions:
- Include proper documentation
- Pass automated tests
- Follow coding standards
- Include security warnings where appropriate

## 📄 License

This project contains modifications to:
- XNU kernel (Apple Public Source License)
- GRUB bootloader (GNU General Public License)

Please review the respective licenses before use.

## 🙏 Acknowledgments

- Apple for open-sourcing XNU kernel
- GNU GRUB project
- QEMU project
- Asahi Linux project for ARM64 macOS research
- m1n1 bootloader project for Apple Silicon insights

## 📖 Step-by-Step Implementation Guide

### Phase 1: Environment Setup

1. **Prepare Host System**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install dependencies
   sudo apt install -y build-essential git clang gcc-aarch64-linux-gnu \
       qemu-system-arm qemu-efi-aarch64 python3 python3-pip \
       autotools-dev autoconf automake bison flex gettext

   # Install Python dependencies
   pip3 install pexpect
   ```

2. **Clone Sources**
   ```bash
   # Create workspace
   mkdir qemu-xnu-workspace && cd qemu-xnu-workspace

   # Clone this project
   git clone <this-repo> qemu-xnu-arm64
   cd qemu-xnu-arm64

   # Clone XNU and GRUB
   git clone https://github.com/apple/darwin-xnu.git xnu
   git clone https://git.savannah.gnu.org/git/grub.git grub
   ```

### Phase 2: Apply Patches

1. **Apply XNU Patches**
   ```bash
   cd xnu

   # Apply each patch in order
   patch -p1 < ../xnu-patches/0001-arm64-remove-apple-silicon-dependencies.patch
   patch -p1 < ../xnu-patches/0002-devicetree-qemu-compatibility.patch
   patch -p1 < ../xnu-patches/0003-memory-management-qemu.patch
   patch -p1 < ../xnu-patches/0004-secure-boot-bypass.patch
   patch -p1 < ../xnu-patches/0005-disable-apple-security-features.patch

   cd ..
   ```

2. **Verify Patches Applied**
   ```bash
   # Check for QEMU compatibility functions
   grep -r "qemu_is_secure_boot_bypassed" xnu/
   grep -r "machine_is_qemu_mode" xnu/
   ```

### Phase 3: Build Components

1. **Build XNU Kernel**
   ```bash
   cd xnu

   # Set build environment
   export ARCH=arm64
   export PLATFORM=QEMU
   export TARGET_CONFIGS="QEMU ARM64"

   # Build kernel
   make -j$(nproc) ARCH=arm64 PLATFORM=QEMU

   # Verify build
   ls BUILD/obj/QEMU_ARM64/kernel
   cd ..
   ```

2. **Build GRUB Bootloader**
   ```bash
   cd grub

   # Generate configure script
   ./autogen.sh

   # Configure for ARM64 EFI with XNU support
   ./configure \
       --target=aarch64-linux-gnu \
       --with-platform=efi \
       --enable-arm64-xnu \
       --prefix=$(pwd)/../install \
       --disable-werror

   # Build and install
   make -j$(nproc)
   make install

   cd ..
   ```

3. **Create GRUB EFI Image**
   ```bash
   # Create EFI firmware image
   install/bin/grub-mkimage \
       --format=arm64-efi \
       --output=install/bin/grub-arm64-efi.fd \
       --prefix=/EFI/GRUB \
       part_gpt part_msdos fat ext2 normal boot linux \
       configfile loopback chain efifwsetup efi_gop \
       efi_uga ls search search_label search_fs_uuid \
       search_fs_file gfxterm gfxterm_background \
       gfxterm_menu test all_video loadenv exfat \
       xnu_arm64 apple_dt apple_memory apple_cache \
       apple_bootargs xnu_kernel apple_efi apple_boot
   ```

### Phase 4: Configure QEMU

1. **Create GRUB Configuration**
   ```bash
   mkdir -p install/share/grub
   cat > install/share/grub/grub.cfg << 'EOF'
   set timeout=10
   insmod xnu_arm64

   set qnu_qemu_mode=1
   set xnu_no_apple_silicon=1
   set xnu_standard_arm64=1
   set qemu_bypass_secure_boot=1
   set no_secure_boot=1

   menuentry "macOS XNU (QEMU ARM64)" {
       echo "Loading XNU kernel for QEMU ARM64..."
       xnu_kernel64 /kernel.patched qemu_bypass_secure_boot no_secure_boot -v debug=0x14e
       echo "Booting XNU kernel..."
       boot
   }
   EOF
   ```

2. **Create EFI Variables**
   ```bash
   # Create EFI variables file
   dd if=/dev/zero of=install/efi-vars.fd bs=1M count=64
   ```

3. **Setup Disk Image**
   ```bash
   # Create disk image
   qemu-img create -f qcow2 install/macos-disk.img 64G
   ```

### Phase 5: Test the System

1. **Launch QEMU**
   ```bash
   # Copy launch script
   cp qemu-config/qemu-macos-arm64.sh install/bin/
   chmod +x install/bin/qemu-macos-arm64.sh

   # Launch QEMU
   cd install
   ./bin/qemu-macos-arm64.sh
   ```

2. **Verify Boot Process**
   - GRUB menu should appear
   - Select "macOS XNU (QEMU ARM64)"
   - Watch for QEMU detection messages
   - Verify security bypass messages

3. **Run Automated Tests**
   ```bash
   # Copy test script
   cp qemu-config/test-xnu-qemu.py install/bin/
   chmod +x install/bin/test-xnu-qemu.py

   # Run tests
   ./bin/test-xnu-qemu.py \
       --grub-efi ./bin/grub-arm64-efi.fd \
       --xnu-kernel ./bin/kernel.patched
   ```

### Phase 6: Debugging and Optimization

1. **Enable Debug Mode**
   ```bash
   # Launch with debugging
   DEBUG=1 ./bin/qemu-macos-arm64.sh

   # In another terminal, connect GDB
   gdb-multiarch
   (gdb) target remote localhost:1234
   (gdb) continue
   ```

2. **Monitor Logs**
   ```bash
   # Watch serial output
   tail -f test-logs/qemu-serial.log

   # Check debug log
   tail -f test-logs/qemu-debug.log
   ```

3. **Common Fixes**
   ```bash
   # If GRUB doesn't start
   ls -la bin/grub-arm64-efi.fd
   qemu-system-aarch64 --version

   # If XNU module missing
   ls lib/grub/arm64-efi/xnu_arm64.mod

   # If kernel panics
   grep "panic" test-logs/qemu-serial.log
   ```

---

**Remember**: This is for educational and development purposes only. Use responsibly and in compliance with applicable laws and licenses.
