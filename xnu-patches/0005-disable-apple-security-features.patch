From: GRUB ARM64 XNU Project <<EMAIL>>
Date: Sat, 12 Jul 2025 00:00:00 +0000
Subject: [PATCH 5/8] security: Disable Apple-specific security features for QEMU

This patch disables Apple-specific security features that prevent
XNU from booting on non-Apple hardware in QEMU environment.

WARNING: This patch significantly reduces system security and should
only be used for development and testing purposes.

Signed-off-by: GRUB ARM64 XNU Project <<EMAIL>>
---
 osfmk/arm64/machine_routines.c   | 55 +++++++++++++++++++++++++++++++
 security/mac_vfs.c               | 25 ++++++++++++++
 bsd/kern/kern_cs.c               | 40 ++++++++++++++++++++++
 osfmk/kern/machine.c             | 30 ++++++++++++++++
 4 files changed, 150 insertions(+)

diff --git a/osfmk/arm64/machine_routines.c b/osfmk/arm64/machine_routines.c
index bcdefgh..ijklmno 100644
--- a/osfmk/arm64/machine_routines.c
+++ b/osfmk/arm64/machine_routines.c
@@ -89,6 +89,61 @@ extern void fleh_fiq_generic(void);
 
 extern uint32_t __unused_0xCC_data[];
 
+/* Disable Apple-specific security features for QEMU */
+static void
+machine_disable_apple_security_features(void)
+{
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("MACHINE: Disabling Apple security features for QEMU\n");
+        
+        /* Disable pointer authentication */
+        uint64_t sctlr_el1;
+        __asm__ volatile("mrs %0, sctlr_el1" : "=r"(sctlr_el1));
+        sctlr_el1 &= ~(1ULL << 30);  /* Clear EnIA bit */
+        sctlr_el1 &= ~(1ULL << 27);  /* Clear EnIB bit */
+        sctlr_el1 &= ~(1ULL << 13);  /* Clear EnDA bit */
+        sctlr_el1 &= ~(1ULL << 12);  /* Clear EnDB bit */
+        __asm__ volatile("msr sctlr_el1, %0" : : "r"(sctlr_el1));
+        __asm__ volatile("isb");
+        
+        printf("MACHINE: Pointer authentication disabled\n");
+        
+        /* Disable memory tagging */
+        uint64_t tcr_el1;
+        __asm__ volatile("mrs %0, tcr_el1" : "=r"(tcr_el1));
+        tcr_el1 &= ~(1ULL << 58);  /* Clear TCMA1 */
+        tcr_el1 &= ~(1ULL << 57);  /* Clear TCMA0 */
+        __asm__ volatile("msr tcr_el1, %0" : : "r"(tcr_el1));
+        __asm__ volatile("isb");
+        
+        printf("MACHINE: Memory tagging disabled\n");
+        
+        /* Disable other Apple-specific features */
+        /* This would include disabling:
+         * - Secure Enclave communication
+         * - Apple-specific PMU features
+         * - Hardware security features
+         * - Apple-specific interrupt handling
+         */
+        
+        printf("MACHINE: Apple security features disabled for QEMU compatibility\n");
+    }
+}
+
+/* Check if Apple security features should be disabled */
+static bool
+machine_should_disable_apple_security(void)
+{
+    /* Disable in QEMU mode */
+    if (qemu_is_secure_boot_bypassed()) {
+        return true;
+    }
+    
+    /* Check for explicit disable flag */
+    /* This could be set via boot arguments */
+    
+    return false;
+}
+
 /* QEMU ARM64 CPU feature detection */
 static void
 machine_detect_qemu_cpu_features(void)
@@ -142,6 +197,16 @@ machine_startup(__unused boot_args * args)
         machine_detect_qemu_cpu_features();
     }
 
+    /* Disable Apple security features if needed */
+    if (machine_should_disable_apple_security()) {
+        machine_disable_apple_security_features();
+    }
+
 	machine_conf();
 
 	/*
diff --git a/security/mac_vfs.c b/security/mac_vfs.c
index 7890123..pqrstuv 100644
--- a/security/mac_vfs.c
+++ b/security/mac_vfs.c
@@ -67,6 +67,7 @@
 #include <security/mac_internal.h>
 #include <security/mac_policy.h>
 #include <security/mac_framework.h>
+#include <security/mac_framework.h>  /* For QEMU bypass macros */
 
 #include <kern/assert.h>
 #include <kern/kalloc.h>
@@ -156,6 +157,10 @@ mac_vnode_check_access(vfs_context_t ctx, struct vnode *vp,
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
+
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -180,6 +185,9 @@ mac_vnode_check_chdir(vfs_context_t ctx, struct vnode *dvp)
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -204,6 +212,9 @@ mac_vnode_check_chroot(vfs_context_t ctx, struct vnode *dvp,
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -228,6 +239,9 @@ mac_vnode_check_create(vfs_context_t ctx, struct vnode *dvp,
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -252,6 +266,9 @@ mac_vnode_check_deleteextattr(vfs_context_t ctx, struct vnode *vp,
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -276,6 +293,9 @@ mac_vnode_check_exchangedata(vfs_context_t ctx,
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -300,6 +320,9 @@ mac_vnode_check_exec(vfs_context_t ctx, struct vnode *vp,
 {
 	kauth_cred_t cred;
 	int error = 0;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
@@ -330,6 +353,9 @@ mac_vnode_check_fsgetpath(vfs_context_t ctx, struct vnode *vp)
 {
 	kauth_cred_t cred;
 	int error;
+    
+    /* Bypass MAC checks in QEMU mode */
+    QEMU_SECURITY_BYPASS_CHECK();
 
 #if SECURITY_MAC_CHECK_ENFORCE
 	/* 21167099 - only check if we allow write */
diff --git a/bsd/kern/kern_cs.c b/bsd/kern/kern_cs.c
index 8901234..wxyzabc 100644
--- a/bsd/kern/kern_cs.c
+++ b/bsd/kern/kern_cs.c
@@ -45,6 +45,7 @@
 #include <kern/kalloc.h>
 #include <libkern/libkern.h>
 #include <mach/vm_types.h>
+#include <security/mac_framework.h>
 
 #include <kern/cs_blobs.h>
 #include <kern/machine.h>
@@ -89,6 +90,45 @@ unsigned long cs_procs_invalidated = 0;
 
 int cs_debug = 0;
 
+/* QEMU code signing bypass functions */
+static int
+qemu_cs_enforcement_disable(void)
+{
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("CS: Disabling code signing enforcement for QEMU\n");
+        
+        /* Disable global code signing enforcement */
+        cs_enforcement_enable = 0;
+        cs_library_val_enable = 0;
+        
+        /* Allow unsigned code */
+        cs_debug = 1;  /* Enable debug mode */
+        
+        printf("CS: Code signing enforcement disabled\n");
+        return 1;  /* Enforcement disabled */
+    }
+    
+    return 0;  /* Use normal enforcement */
+}
+
+/* Check if code signing should be bypassed for a process */
+static bool
+qemu_should_bypass_cs_for_proc(proc_t p)
+{
+    if (qemu_is_secure_boot_bypassed()) {
+        /* Bypass for all processes in QEMU mode */
+        return true;
+    }
+    
+    return false;
+}
+
+/* Initialize QEMU code signing bypass */
+void
+qemu_cs_init_bypass(void)
+{
+    qemu_cs_enforcement_disable();
+}
+
 #if DEVELOPMENT || DEBUG
 SYSCTL_INT(_vm, OID_AUTO, cs_force_kill, CTLFLAG_RW | CTLFLAG_LOCKED, &cs_force_kill, 0, "");
 SYSCTL_INT(_vm, OID_AUTO, cs_force_hard, CTLFLAG_RW | CTLFLAG_LOCKED, &cs_force_hard, 0, "");
@@ -156,6 +196,16 @@ cs_enforcement(struct proc *p)
 {
 	if (cs_enforcement_panic) {
 		panic("cs_enforcement called");
+    }
+    
+    /* Check for QEMU bypass */
+    if (qemu_should_bypass_cs_for_proc(p)) {
+        /* Always return false (no enforcement) in QEMU mode */
+        return 0;
+    }
+    
+    /* Check global enforcement disable */
+    if (!cs_enforcement_enable) {
+        return 0;
 	}
 
 	return cs_enforcement_enable;
diff --git a/osfmk/kern/machine.c b/osfmk/kern/machine.c
index 9012345..defghij 100644
--- a/osfmk/kern/machine.c
+++ b/osfmk/kern/machine.c
@@ -67,6 +67,7 @@
 #include <machine/machine_routines.h>
 #include <machine/smp.h>
 #include <console/serial_protos.h>
+#include <security/mac_framework.h>
 
 #include <kern/processor.h>
 #include <kern/cpu_number.h>
@@ -89,6 +90,35 @@ static void processor_doshutdown(processor_t processor);
 
 static LCK_GRP_DECLARE(processor_list_lck_grp, "processor_list");
 
+/* QEMU machine lockdown bypass */
+static bool qemu_lockdown_bypassed = false;
+
+/* Initialize QEMU lockdown bypass */
+static void
+qemu_init_lockdown_bypass(void)
+{
+    if (qemu_is_secure_boot_bypassed()) {
+        qemu_lockdown_bypassed = true;
+        printf("MACHINE: Lockdown bypass enabled for QEMU\n");
+        printf("MACHINE: WARNING - Machine lockdown disabled!\n");
+    }
+}
+
+/* Check if machine lockdown should be bypassed */
+static bool
+qemu_should_bypass_lockdown(void)
+{
+    return qemu_lockdown_bypassed;
+}
+
+/* QEMU-compatible machine lockdown */
+static void
+qemu_machine_lockdown_bypass(void)
+{
+    printf("MACHINE: Applying QEMU-compatible lockdown (minimal restrictions)\n");
+    /* Apply minimal lockdown suitable for QEMU environment */
+}
+
 /*
  *	Exported variables:
  */
@@ -456,6 +486,16 @@ processor_start(processor_t processor)
 void
 machine_lockdown(void)
 {
+    /* Initialize QEMU lockdown bypass */
+    qemu_init_lockdown_bypass();
+    
+    /* Check if lockdown should be bypassed */
+    if (qemu_should_bypass_lockdown()) {
+        qemu_machine_lockdown_bypass();
+        return;  /* Skip normal lockdown */
+    }
+    
+    /* Apply normal machine lockdown */
 #if CONFIG_KERNEL_INTEGRITY
 	/* Ensure that kernel text is read-only and non-executable data is non-executable. */
 	kext_lockdown();
-- 
2.34.1
