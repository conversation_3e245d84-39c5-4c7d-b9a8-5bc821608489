From: GRUB ARM64 XNU Project <<EMAIL>>
Date: Sat, 12 Jul 2025 00:00:00 +0000
Subject: [PATCH 3/8] memory: Adapt memory management for QEMU ARM64

This patch modifies XNU's memory management to work with QEMU's
flexible memory layout instead of Apple's fixed memory regions.

Signed-off-by: GRUB ARM64 XNU Project <<EMAIL>>
---
 osfmk/arm64/pmap.c               | 85 +++++++++++++++++++++++++++++++
 osfmk/arm/pmap.c                 | 45 ++++++++++++++++
 osfmk/vm/vm_map.c                | 35 +++++++++++++
 pexpert/arm/pe_init.c            | 25 +++++++++
 4 files changed, 190 insertions(+)

diff --git a/osfmk/arm64/pmap.c b/osfmk/arm64/pmap.c
index 9012345..ijklmno 100644
--- a/osfmk/arm64/pmap.c
+++ b/osfmk/arm64/pmap.c
@@ -156,6 +156,91 @@ SECURITY_READ_ONLY_LATE(static const pmap_io_range_t*) io_attr_table = NULL;
 
 static const pmap_io_range_t bootstrap_io_attr_table[] = {
 	{
+        /* QEMU ARM64 virt machine memory layout */
+        .addr = 0x00000000ULL,
+        .len  = 0x08000000ULL,  /* 128MB - Flash/ROM area */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x08000000ULL,
+        .len  = 0x00020000ULL,  /* 128KB - GIC distributor */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x08010000ULL,
+        .len  = 0x00010000ULL,  /* 64KB - GIC CPU interface */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x09000000ULL,
+        .len  = 0x00001000ULL,  /* 4KB - UART */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x09010000ULL,
+        .len  = 0x00001000ULL,  /* 4KB - RTC */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x0a000000ULL,
+        .len  = 0x00200000ULL,  /* 2MB - PCIe MMIO */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x10000000ULL,
+        .len  = 0x2eff0000ULL,  /* PCIe ECAM and high MMIO */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        /* Main memory area - will be adjusted based on actual memory size */
+        .addr = 0x40000000ULL,
+        .len  = 0x80000000ULL,  /* 2GB default, will be updated */
+        .wimg = PMAP_IO_RANGE_WRITEBACK,
+        .signature = 0xDEADBEEF,
+    },
+    {
 		.addr = 0,
 		.len = 0,
 		.wimg = 0,
@@ -163,6 +248,6 @@ static const pmap_io_range_t bootstrap_io_attr_table[] = {
 	}
 };
 
+/* QEMU memory layout configuration */
+static bool qemu_memory_layout_configured = false;
+static uint64_t qemu_memory_base = 0x40000000ULL;
+static uint64_t qemu_memory_size = 0x80000000ULL;  /* 2GB default */
+
+/* Configure QEMU memory layout */
+void
+pmap_configure_qemu_memory(uint64_t base, uint64_t size)
+{
+    if (machine_is_qemu_mode()) {
+        qemu_memory_base = base;
+        qemu_memory_size = size;
+        qemu_memory_layout_configured = true;
+        
+        printf("PMAP: QEMU memory configured - base: 0x%llx, size: 0x%llx\n", 
+               base, size);
+        
+        /* Update the bootstrap table */
+        pmap_io_range_t *table = (pmap_io_range_t *)bootstrap_io_attr_table;
+        for (int i = 0; table[i].len != 0; i++) {
+            if (table[i].addr == 0x40000000ULL) {
+                table[i].addr = base;
+                table[i].len = size;
+                break;
+            }
+        }
+    }
+}
+
+/* Get QEMU memory layout */
+void
+pmap_get_qemu_memory_layout(uint64_t *base, uint64_t *size)
+{
+    if (base) *base = qemu_memory_base;
+    if (size) *size = qemu_memory_size;
+}
+
+/* Check if address is in QEMU memory range */
+bool
+pmap_is_qemu_memory_address(uint64_t addr)
+{
+    if (!machine_is_qemu_mode()) {
+        return false;
+    }
+    
+    return (addr >= qemu_memory_base && 
+            addr < (qemu_memory_base + qemu_memory_size));
+}
+
 /*
  * pmap_map_globals_disable_text_exec configures the page table entries for the globals to remove
  * execute permissions.
@@ -1850,6 +1935,16 @@ pmap_bootstrap(vm_offset_t vstart)
 	const pmap_io_range_t *ranges = NULL;
 	unsigned int ranges_count = 0;
 
+    /* Configure QEMU memory layout early */
+    if (machine_is_qemu_mode() && !qemu_memory_layout_configured) {
+        /* Get memory info from boot args or device tree */
+        uint64_t mem_base, mem_size;
+        pmap_get_memory_from_boot_args(&mem_base, &mem_size);
+        pmap_configure_qemu_memory(mem_base, mem_size);
+        
+        printf("PMAP: QEMU bootstrap memory layout configured\n");
+    }
+
 	vm_first_phys = gPhysBase;
 	vm_last_phys = gPhysBase + gPhysSize - 1;
 
@@ -1857,7 +1952,11 @@ pmap_bootstrap(vm_offset_t vstart)
 	 * The kernel and page tables must be mapped within the first 512GB of RAM.
 	 */
 	if (!(((gVirtBase & 0xFFFFFF8000000000ULL) == 0) && (gPhysBase < 0x8000000000ULL))) {
-		panic("Unsupported memory configuration %p %p", (void*)gVirtBase, (void*)gPhysBase);
+        if (!machine_is_qemu_mode()) {
+            panic("Unsupported memory configuration %p %p", (void*)gVirtBase, (void*)gPhysBase);
+        } else {
+            printf("PMAP: QEMU mode - relaxing memory configuration checks\n");
+        }
 	}
 
 	avail_start = first_avail;
diff --git a/osfmk/arm/pmap.c b/osfmk/arm/pmap.c
index 0123456..nopqrst 100644
--- a/osfmk/arm/pmap.c
+++ b/osfmk/arm/pmap.c
@@ -234,6 +234,51 @@ SECURITY_READ_ONLY_LATE(static const pmap_io_range_t*) io_attr_table = NULL;
 
 static const pmap_io_range_t bootstrap_io_attr_table[] = {
 	{
+        /* QEMU ARM64 virt machine I/O ranges */
+        .addr = 0x08000000ULL,
+        .len  = 0x00020000ULL,  /* GIC distributor */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x08010000ULL,
+        .len  = 0x00010000ULL,  /* GIC CPU interface */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
+        .addr = 0x09000000ULL,
+        .len  = 0x00001000ULL,  /* UART */
+        .wimg = PMAP_IO_RANGE_STRONG_ORDER,
+        .signature = 0xDEADBEEF,
+    },
+    {
 		.addr = 0,
 		.len = 0,
 		.wimg = 0,
@@ -241,6 +286,6 @@ static const pmap_io_range_t bootstrap_io_attr_table[] = {
 	}
 };
 
+/* Get memory information from boot arguments */
+void
+pmap_get_memory_from_boot_args(uint64_t *base, uint64_t *size)
+{
+    boot_args *args = (boot_args *)PE_state.bootArgs;
+    
+    if (machine_is_qemu_mode()) {
+        /* For QEMU, use the memory information from boot args */
+        if (args && args->memSize > 0) {
+            *base = args->physBase;
+            *size = args->memSize;
+        } else {
+            /* Default QEMU memory layout */
+            *base = 0x40000000ULL;  /* 1GB base */
+            *size = 0x80000000ULL;  /* 2GB size */
+        }
+        
+        printf("PMAP: QEMU memory from boot args - base: 0x%llx, size: 0x%llx\n", 
+               *base, *size);
+    } else {
+        /* Apple Silicon memory layout */
+        *base = gPhysBase;
+        *size = gPhysSize;
+    }
+}
+
 /*
  * pmap_map_globals_disable_text_exec configures the page table entries for the globals to remove
  * execute permissions.
@@ -1234,6 +1279,16 @@ pmap_bootstrap(vm_offset_t vstart)
 	const pmap_io_range_t *ranges = NULL;
 	unsigned int ranges_count = 0;
 
+    /* Handle QEMU memory layout */
+    if (machine_is_qemu_mode()) {
+        printf("PMAP: Configuring for QEMU ARM64 environment\n");
+        
+        /* Use QEMU-specific memory ranges */
+        ranges = bootstrap_io_attr_table;
+        ranges_count = sizeof(bootstrap_io_attr_table) / sizeof(bootstrap_io_attr_table[0]) - 1;
+        
+        printf("PMAP: Using QEMU I/O attribute table with %d ranges\n", ranges_count);
+    }
+
 	vm_first_phys = gPhysBase;
 	vm_last_phys = gPhysBase + gPhysSize - 1;
 
diff --git a/osfmk/vm/vm_map.c b/osfmk/vm/vm_map.c
index 2345678..uvwxyza 100644
--- a/osfmk/vm/vm_map.c
+++ b/osfmk/vm/vm_map.c
@@ -156,6 +156,41 @@ unsigned int vm_map_set_cache_attr_count = 0;
 
 kern_return_t vm_map_exec_lockdown(vm_map_t map);
 
+/* QEMU-specific VM map configuration */
+static bool qemu_vm_map_configured = false;
+
+/* Configure VM map for QEMU environment */
+void
+vm_map_configure_qemu(void)
+{
+    if (machine_is_qemu_mode() && !qemu_vm_map_configured) {
+        printf("VM: Configuring VM map for QEMU environment\n");
+        
+        /* Adjust VM map parameters for QEMU */
+        /* QEMU typically has different memory layout constraints */
+        
+        /* Disable Apple-specific VM optimizations */
+        vm_map_jit_entry_exists = FALSE;
+        
+        /* Configure for standard ARM64 without Apple extensions */
+        qemu_vm_map_configured = true;
+        
+        printf("VM: QEMU VM map configuration complete\n");
+    }
+}
+
+/* Check if VM address is valid for QEMU */
+bool
+vm_map_is_qemu_address_valid(vm_map_offset_t address)
+{
+    if (!machine_is_qemu_mode()) {
+        return true;  /* Use standard validation */
+    }
+    
+    /* QEMU-specific address validation */
+    /* Allow more flexible address ranges */
+    return (address >= VM_MIN_ADDRESS && address <= VM_MAX_ADDRESS);
+}
+
 /*
  *	vm_map_init:
  *
@@ -166,6 +201,16 @@ void
 vm_map_init(
 	void)
 {
+    /* Configure for QEMU if needed */
+    if (machine_is_qemu_mode()) {
+        vm_map_configure_qemu();
+    }
+
 	vm_map_zone = zinit((vm_map_size_t) sizeof(struct _vm_map), 40*1024,
 			    PAGE_SIZE, "vm_map");
 	zone_change(vm_map_zone, Z_NOENCRYPT, TRUE);
diff --git a/pexpert/arm/pe_init.c b/pexpert/arm/pe_init.c
index fghijkl..mnopqrs 100644
--- a/pexpert/arm/pe_init.c
+++ b/pexpert/arm/pe_init.c
@@ -143,6 +143,31 @@ qemu_dt_parse_memory(void)
         return -1;
     }
     
+    /* Try to get memory info from boot args first */
+    boot_args *args = (boot_args *)PE_state.bootArgs;
+    if (args && args->memSize > 0) {
+        gPhysBase = args->physBase;
+        gPhysSize = args->memSize;
+        
+        printf("PE: Memory from boot args - base: 0x%llx, size: 0x%llx\n", 
+               gPhysBase, gPhysSize);
+        return 0;
+    }
+    
+    /* Parse memory from device tree */
+    /* This would implement proper FDT memory parsing */
+    /* For now, detect common QEMU configurations */
+    
+    /* Check for common QEMU memory sizes */
+    if (gPhysSize == 0) {
+        /* Default to 2GB if not specified */
+        gPhysBase = 0x40000000ULL;
+        gPhysSize = 0x80000000ULL;
+        
+        printf("PE: Using default QEMU memory layout\n");
+    }
+    
     /* Set default memory configuration for QEMU */
     gPhysBase = 0x40000000;  /* QEMU ARM64 virt machine base */
     gPhysSize = 0x80000000;  /* 2GB default */
-- 
2.34.1
