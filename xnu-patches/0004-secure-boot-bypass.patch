From: GRUB ARM64 XNU Project <<EMAIL>>
Date: Sat, 12 Jul 2025 00:00:00 +0000
Subject: [PATCH 4/8] security: Bypass Apple secure boot for QEMU compatibility

This patch bypasses Apple's secure boot verification and code signing
requirements to enable XNU booting in QEMU virtualization environment.

WARNING: This patch disables security features and should only be used
for development and testing purposes in controlled environments.

Signed-off-by: GRUB ARM64 XNU Project <<EMAIL>>
---
 osfmk/kern/startup.c             | 45 +++++++++++++++++++++++++++++++
 security/mac_framework.h         | 20 ++++++++++++++
 libkern/img4/img4.c              | 35 ++++++++++++++++++++++++
 osfmk/arm/arm_init.c             | 25 +++++++++++++++++
 bsd/kern/kern_exec.c             | 30 ++++++++++++++++++++
 5 files changed, 155 insertions(+)

diff --git a/osfmk/kern/startup.c b/osfmk/kern/startup.c
index 3456789..cdefghi 100644
--- a/osfmk/kern/startup.c
+++ b/osfmk/kern/startup.c
@@ -89,6 +89,51 @@ extern void kdp_init(void);
 
 extern void alternate_debugger_init(void);
 
+/* QEMU secure boot bypass */
+static bool qemu_secure_boot_disabled = false;
+
+/* Check if secure boot should be bypassed for QEMU */
+static bool
+should_bypass_secure_boot(void)
+{
+    /* Check for QEMU environment indicators */
+    if (machine_is_qemu_mode()) {
+        return true;
+    }
+    
+    /* Check boot arguments for bypass flag */
+    boot_args *args = (boot_args *)PE_state.bootArgs;
+    if (args && args->cmdline) {
+        if (strstr(args->cmdline, "qemu_bypass_secure_boot") != NULL) {
+            return true;
+        }
+        if (strstr(args->cmdline, "no_secure_boot") != NULL) {
+            return true;
+        }
+    }
+    
+    return false;
+}
+
+/* Initialize secure boot bypass for QEMU */
+void
+qemu_init_secure_boot_bypass(void)
+{
+    if (should_bypass_secure_boot()) {
+        qemu_secure_boot_disabled = true;
+        
+        printf("SECURITY: Secure boot bypass enabled for QEMU\n");
+        printf("SECURITY: WARNING - Security features disabled!\n");
+        printf("SECURITY: This should only be used for development/testing\n");
+    }
+}
+
+/* Check if secure boot is bypassed */
+bool
+qemu_is_secure_boot_bypassed(void)
+{
+    return qemu_secure_boot_disabled;
+}
+
 /*
  *	Running in virtual memory, on the interrupt stack.
  */
@@ -96,6 +141,16 @@ extern void alternate_debugger_init(void);
 void
 kernel_bootstrap(void)
 {
+    /* Initialize secure boot bypass early for QEMU */
+    qemu_init_secure_boot_bypass();
+    
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("KERNEL: Starting with secure boot bypass (QEMU mode)\n");
+    } else {
+        printf("KERNEL: Starting with secure boot enabled\n");
+    }
+
 	kernel_bootstrap_log("kernel_bootstrap");
 
 	if (PE_parse_boot_argn("show_pointers", &show_pointers, sizeof(show_pointers))) {
@@ -156,6 +211,16 @@ kernel_bootstrap(void)
 	 */
 	kernel_bootstrap_log("calling machine_lockdown");
 	machine_lockdown();
+    
+    /* Skip lockdown in QEMU mode */
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("KERNEL: Skipping machine lockdown for QEMU\n");
+        /* Don't call machine_lockdown() in QEMU mode */
+    } else {
+        printf("KERNEL: Applying machine lockdown\n");
+        machine_lockdown();
+    }
+
 
 	kernel_bootstrap_log("calling lck_mod_init");
 	lck_mod_init();
diff --git a/security/mac_framework.h b/security/mac_framework.h
index 4567890..defghij 100644
--- a/security/mac_framework.h
+++ b/security/mac_framework.h
@@ -45,6 +45,26 @@
 #include <sys/kernel_types.h>
 #include <mach/kmod.h>
 
+/* QEMU security bypass declarations */
+extern bool qemu_is_secure_boot_bypassed(void);
+extern void qemu_init_secure_boot_bypass(void);
+
+/* Macro to bypass security checks in QEMU mode */
+#define QEMU_SECURITY_BYPASS_CHECK() \
+    do { \
+        if (qemu_is_secure_boot_bypassed()) { \
+            return 0; /* Allow operation */ \
+        } \
+    } while (0)
+
+/* Macro to bypass security checks with custom return value */
+#define QEMU_SECURITY_BYPASS_CHECK_RETURN(retval) \
+    do { \
+        if (qemu_is_secure_boot_bypassed()) { \
+            return (retval); \
+        } \
+    } while (0)
+
 struct mac_module_data;
 
 /*
diff --git a/libkern/img4/img4.c b/libkern/img4/img4.c
index 5678901..efghijk 100644
--- a/libkern/img4/img4.c
+++ b/libkern/img4/img4.c
@@ -34,6 +34,7 @@
 #include <img4/img4.h>
 #include <libkern/libkern.h>
 #include <kern/assert.h>
+#include <security/mac_framework.h>
 
 #if IMG4_TAPI
 #include "tapi.h"
@@ -89,6 +90,40 @@ img4_get_trusted_external_payload(img4_runtime_t rt,
 	return img4if->i4if_v7.get_trusted_external_payload(rt, tag, obj, len);
 }
 
+/* QEMU IMG4 bypass functions */
+static int
+qemu_img4_verify_bypass(const img4_buff_t *buff, 
+                       const img4_environment_t *env,
+                       const img4_nonce_domain_t *nonce_domain)
+{
+    /* Always succeed in QEMU mode */
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("IMG4: Bypassing verification for QEMU\n");
+        return 0;
+    }
+    
+    /* Use original verification */
+    return -1; /* Will fall through to original function */
+}
+
+static int
+qemu_img4_get_object_bypass(const img4_buff_t *buff,
+                           img4_tag_t tag,
+                           const img4_object_t **obj,
+                           size_t *len)
+{
+    /* In QEMU mode, create a dummy object */
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("IMG4: Bypassing object verification for QEMU\n");
+        *obj = (const img4_object_t *)buff->i4b_bytes;
+        *len = buff->i4b_len;
+        return 0;
+    }
+    
+    /* Use original function */
+    return -1; /* Will fall through to original function */
+}
+
 int
 img4_verify(const img4_buff_t *buff, const img4_environment_t *env,
     const img4_nonce_domain_t *nonce_domain)
@@ -96,6 +131,16 @@ img4_verify(const img4_buff_t *buff, const img4_environment_t *env,
 	const img4_interface_t *img4if = img4_interface_get();
 	if (!img4if) {
 		return ENODEV;
+    }
+    
+    /* Check for QEMU bypass */
+    if (qemu_is_secure_boot_bypassed()) {
+        int bypass_result = qemu_img4_verify_bypass(buff, env, nonce_domain);
+        if (bypass_result == 0) {
+            return 0; /* Bypass successful */
+        }
+        /* Fall through to original verification if bypass fails */
+    }
 	}
 
 	return img4if->i4if_v4.verify(buff, env, nonce_domain);
@@ -107,6 +152,16 @@ img4_get_object(const img4_buff_t *buff, img4_tag_t tag,
 	const img4_interface_t *img4if = img4_interface_get();
 	if (!img4if) {
 		return ENODEV;
+    }
+    
+    /* Check for QEMU bypass */
+    if (qemu_is_secure_boot_bypassed()) {
+        int bypass_result = qemu_img4_get_object_bypass(buff, tag, obj, len);
+        if (bypass_result == 0) {
+            return 0; /* Bypass successful */
+        }
+        /* Fall through to original function if bypass fails */
+    }
 	}
 
 	return img4if->i4if_v4.get_object(buff, tag, obj, len);
diff --git a/osfmk/arm/arm_init.c b/osfmk/arm/arm_init.c
index cdefghi..jklmnop 100644
--- a/osfmk/arm/arm_init.c
+++ b/osfmk/arm/arm_init.c
@@ -360,6 +360,31 @@ arm_init(
 	DTEntry chosen = NULL;
 	unsigned int dt_entry_size = 0;
 
+    /* QEMU compatibility: Skip Apple-specific security initialization */
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("ARM_INIT: Skipping Apple security initialization for QEMU\n");
+        
+        /* Skip Apple slide and signing */
+        goto skip_apple_security;
+    }
+
+    /* Original Apple security initialization */
+    arm_setup_pre_sign();
+    arm_slide_rebase_and_sign_image();
+
+skip_apple_security:
+    /* Continue with boot args setup */
+    if (qemu_is_secure_boot_bypassed()) {
+        /* In QEMU mode, use boot args directly without integrity checks */
+        BootArgs = args;
+        printf("ARM_INIT: Using direct boot args for QEMU (integrity checks disabled)\n");
+    } else {
+        /* Use constant copy with integrity checks */
+        const_boot_args = *args;
+        BootArgs = args = &const_boot_args;
+        printf("ARM_INIT: Using integrity-checked boot args\n");
+    }
+
     /* QEMU compatibility: Skip Apple-specific initialization */
     if (machine_is_qemu_mode()) {
         printf("XNU: Skipping Apple-specific ARM initialization for QEMU\n");
diff --git a/bsd/kern/kern_exec.c b/bsd/kern/kern_exec.c
index 6789012..mnopqrs 100644
--- a/bsd/kern/kern_exec.c
+++ b/bsd/kern/kern_exec.c
@@ -89,6 +89,7 @@
 #include <kern/assert.h>
 #include <kern/task.h>
 #include <kern/coalition.h>
+#include <security/mac_framework.h>
 
 #include <security/audit/audit.h>
 
@@ -156,6 +157,35 @@ static int exec_handle_spawnattr_policy(proc_t p, int psa_apptype, uint64_t psa
 
 static int exec_handle_port_actions(struct image_params *imgp, short psa_flags, boolean_t * portwatch_present, ipc_port_t * portwatch_ports);
 
+/* QEMU code signing bypass */
+static int
+qemu_bypass_code_signing_check(proc_t p, struct image_params *imgp)
+{
+    /* Bypass code signing in QEMU mode */
+    if (qemu_is_secure_boot_bypassed()) {
+        printf("EXEC: Bypassing code signing for QEMU: %s\n", 
+               imgp->ip_ndp->ni_cnd.cn_nameptr);
+        
+        /* Mark as platform binary to bypass restrictions */
+        imgp->ip_flags |= IMGPF_PLATFORM_BINARY;
+        
+        /* Clear any code signing requirements */
+        imgp->ip_csflags &= ~(CS_REQUIRE_LV | CS_ENFORCEMENT | CS_KILL);
+        
+        /* Set flags to indicate bypass */
+        imgp->ip_csflags |= CS_VALID | CS_SIGNED;
+        
+        return 0; /* Allow execution */
+    }
+    
+    return -1; /* Use normal code signing */
+}
+
+/* Check if code signing should be bypassed */
+static bool should_bypass_code_signing(struct image_params *imgp)
+{
+    return qemu_is_secure_boot_bypassed();
+}
+
 /*
  * exec_add_apple_strings
  *
@@ -456,6 +486,16 @@ execve(proc_t p, struct execve_args *uap, int32_t *retval)
 	if (error)
 		goto exit_with_error;
 
+    /* QEMU code signing bypass */
+    if (should_bypass_code_signing(&image_params)) {
+        error = qemu_bypass_code_signing_check(p, &image_params);
+        if (error == 0) {
+            printf("EXEC: Code signing bypassed for QEMU\n");
+            /* Continue with execution */
+        }
+        /* If bypass returns -1, continue with normal code signing */
+    }
+
 	/* Copy; avoid invocation of an interpreter overwriting the original */
 	for (i = 0; i < argc; i++) {
 		user_addr_t argv_string_addr = argv[i];
-- 
2.34.1
