From: GRUB ARM64 XNU Project <<EMAIL>>
Date: Sat, 12 Jul 2025 00:00:00 +0000
Subject: [PATCH 1/8] arm64: Remove Apple Silicon dependencies for QEMU compatibility

This patch removes Apple Silicon-specific dependencies from XNU kernel
to enable booting on standard ARM64 architecture in QEMU virtualization.

Signed-off-by: GRUB ARM64 XNU Project <<EMAIL>>
---
 osfmk/arm/machine_routines.c     | 45 +++++++++++++++++++++++++++++++
 osfmk/arm64/machine_routines.c   | 38 ++++++++++++++++++++++++++
 osfmk/arm/arm_init.c             | 25 +++++++++++++++++
 pexpert/pexpert/arm64/boot.h     | 15 ++++++++++
 4 files changed, 123 insertions(+)

diff --git a/osfmk/arm/machine_routines.c b/osfmk/arm/machine_routines.c
index 1234567..abcdefg 100644
--- a/osfmk/arm/machine_routines.c
+++ b/osfmk/arm/machine_routines.c
@@ -45,6 +45,15 @@
 #include <arm/misc_protos.h>
 #include <arm/rtclock.h>
 
+/* QEMU ARM64 compatibility flags */
+static bool qemu_arm64_mode = false;
+static bool apple_silicon_disabled = false;
+
+/* Function to detect QEMU environment */
+static bool
+is_qemu_environment(void)
+{
+    /* Check for QEMU-specific device tree properties */
+    DTEntry chosen;
+    const void *prop;
+    unsigned int prop_size;
+    
+    if (SecureDTLookupEntry(NULL, "/chosen", &chosen) == kSuccess) {
+        if (SecureDTGetProperty(chosen, "qemu,firmware", &prop, &prop_size) == kSuccess) {
+            return true;
+        }
+    }
+    
+    /* Check for QEMU machine type */
+    if (SecureDTLookupEntry(NULL, "/", &chosen) == kSuccess) {
+        if (SecureDTGetProperty(chosen, "model", &prop, &prop_size) == kSuccess) {
+            if (strncmp((const char*)prop, "QEMU", 4) == 0) {
+                return true;
+            }
+        }
+    }
+    
+    return false;
+}
+
+/* Initialize QEMU compatibility mode */
+void
+machine_init_qemu_compatibility(void)
+{
+    if (is_qemu_environment()) {
+        qemu_arm64_mode = true;
+        apple_silicon_disabled = true;
+        
+        printf("XNU: QEMU ARM64 compatibility mode enabled\n");
+        printf("XNU: Apple Silicon features disabled\n");
+    }
+}
+
 void
 machine_startup(__unused boot_args * args)
 {
+    /* Initialize QEMU compatibility early */
+    machine_init_qemu_compatibility();
+    
 #if defined(HAS_IPI) && (DEVELOPMENT || DEBUG)
-	if (!PE_parse_boot_argn("fastipi", &gFastIPI, sizeof(gFastIPI))) {
+    /* Skip Apple-specific IPI configuration in QEMU mode */
+    if (!qemu_arm64_mode && !PE_parse_boot_argn("fastipi", &gFastIPI, sizeof(gFastIPI))) {
 		gFastIPI = 1;
 	}
 #endif /* defined(HAS_IPI) && (DEVELOPMENT || DEBUG)*/
 
-
-	machine_conf();
+    /* Skip Apple Silicon machine configuration in QEMU mode */
+    if (!qemu_arm64_mode) {
+        machine_conf();
+    } else {
+        /* Use generic ARM64 machine configuration for QEMU */
+        machine_conf_qemu();
+    }
 
 
 	/*
@@ -78,6 +127,102 @@ machine_startup(__unused boot_args * args)
 	/* NOTREACHED */
 }
 
+/* QEMU-specific machine configuration */
+void
+machine_conf_qemu(void)
+{
+    /* Generic ARM64 configuration for QEMU */
+    machine.memory_size = 0;  /* Will be detected from device tree */
+    machine.max_mem = 0;
+    
+    /* Disable Apple Silicon-specific features */
+    machine.cpu_id = 0;
+    machine.cpu_subtype = CPU_SUBTYPE_ARM64_ALL;
+    machine.cpu_threadtype = CPU_THREADTYPE_NONE;
+    
+    /* Use generic ARM64 cache configuration */
+    machine.cache_line_size = 64;  /* Standard ARM64 cache line size */
+    machine.coherency_line_size = 64;
+    
+    /* Disable Apple-specific hardware features */
+    machine.cpu_capabilities = 0;
+    
+    printf("XNU: QEMU ARM64 machine configuration complete\n");
+}
+
+/* Check if running in QEMU mode */
+bool
+machine_is_qemu_mode(void)
+{
+    return qemu_arm64_mode;
+}
+
+/* Check if Apple Silicon features are disabled */
+bool
+machine_apple_silicon_disabled(void)
+{
+    return apple_silicon_disabled;
+}
+
 typedef void (*invalidate_fn_t)(void);
 
 static SECURITY_READ_ONLY_LATE(invalidate_fn_t) invalidate_hmac_function = NULL;
@@ -95,7 +240,11 @@ set_invalidate_hmac_function(invalidate_fn_t fn)
 bool
 ml_is_secure_hib_supported(void)
 {
-	return false;
+    /* Disable secure hibernation in QEMU mode */
+    if (qemu_arm64_mode) {
+        return false;
+    }
+    return false;  /* Keep disabled for now */
 }
 
 void
diff --git a/osfmk/arm64/machine_routines.c b/osfmk/arm64/machine_routines.c
index 2345678..bcdefgh 100644
--- a/osfmk/arm64/machine_routines.c
+++ b/osfmk/arm64/machine_routines.c
@@ -89,6 +89,44 @@ extern void fleh_fiq_generic(void);
 
 extern uint32_t __unused_0xCC_data[];
 
+/* QEMU ARM64 CPU feature detection */
+static void
+machine_detect_qemu_cpu_features(void)
+{
+    uint64_t midr_el1;
+    
+    /* Read Main ID Register */
+    __asm__ volatile("mrs %0, midr_el1" : "=r"(midr_el1));
+    
+    /* Check if this is a QEMU CPU */
+    uint32_t implementer = (midr_el1 >> 24) & 0xFF;
+    uint32_t part_num = (midr_el1 >> 4) & 0xFFF;
+    
+    if (implementer == 0x51 && part_num == 0xC00) {
+        /* QEMU Cortex-A57 */
+        printf("XNU: Detected QEMU Cortex-A57 CPU\n");
+    } else if (implementer == 0x41) {
+        /* ARM Ltd. implementation */
+        printf("XNU: Detected ARM CPU (implementer: 0x%x, part: 0x%x)\n", 
+               implementer, part_num);
+    } else {
+        printf("XNU: Unknown CPU (implementer: 0x%x, part: 0x%x)\n", 
+               implementer, part_num);
+    }
+    
+    /* Disable Apple-specific CPU features */
+    if (machine_is_qemu_mode()) {
+        /* Disable pointer authentication */
+        uint64_t id_aa64isar1_el1;
+        __asm__ volatile("mrs %0, id_aa64isar1_el1" : "=r"(id_aa64isar1_el1));
+        
+        /* Mask out Apple-specific features */
+        id_aa64isar1_el1 &= ~(0xFFULL << 4);  /* Clear APA field */
+        id_aa64isar1_el1 &= ~(0xFFULL << 8);  /* Clear API field */
+        
+        printf("XNU: Apple-specific CPU features disabled for QEMU\n");
+    }
+}
+
 void
 machine_startup(__unused boot_args * args)
 {
@@ -98,6 +136,10 @@ machine_startup(__unused boot_args * args)
 	}
 #endif /* defined(HAS_IPI) && (DEVELOPMENT || DEBUG)*/
 
+    /* Detect QEMU CPU features early */
+    if (machine_is_qemu_mode()) {
+        machine_detect_qemu_cpu_features();
+    }
 
 	machine_conf();
 
diff --git a/osfmk/arm/arm_init.c b/osfmk/arm/arm_init.c
index 3456789..cdefghi 100644
--- a/osfmk/arm/arm_init.c
+++ b/osfmk/arm/arm_init.c
@@ -360,6 +360,31 @@ arm_init(
 	DTEntry chosen = NULL;
 	unsigned int dt_entry_size = 0;
 
+    /* QEMU compatibility: Skip Apple-specific initialization */
+    if (machine_is_qemu_mode()) {
+        printf("XNU: Skipping Apple-specific ARM initialization for QEMU\n");
+        
+        /* Skip Apple slide and signing */
+        goto skip_apple_init;
+    }
+
 	arm_setup_pre_sign();
 
 	arm_slide_rebase_and_sign_image();
 
+skip_apple_init:
 	/* If kernel integrity is supported, use a constant copy of the boot args. */
-	const_boot_args = *args;
-	BootArgs = args = &const_boot_args;
+    if (machine_is_qemu_mode()) {
+        /* In QEMU mode, use boot args directly without integrity checks */
+        BootArgs = args;
+        printf("XNU: Using direct boot args for QEMU (integrity checks disabled)\n");
+    } else {
+        const_boot_args = *args;
+        BootArgs = args = &const_boot_args;
+    }
+
+    /* Continue with rest of initialization */
+    if (machine_is_qemu_mode()) {
+        printf("XNU: QEMU ARM64 initialization complete\n");
+    }
 
 	/* Get the boot args revision */
diff --git a/pexpert/pexpert/arm64/boot.h b/pexpert/pexpert/arm64/boot.h
index 4567890..defghij 100644
--- a/pexpert/pexpert/arm64/boot.h
+++ b/pexpert/pexpert/arm64/boot.h
@@ -45,6 +45,21 @@
 #define BOOT_LINE_LENGTH        256
 #endif
 
+/* QEMU ARM64 compatibility definitions */
+#define QEMU_ARM64_MAGIC        0x514D5541  /* "QEMU" in little endian */
+#define QEMU_BOOT_ARGS_VERSION  3           /* QEMU-specific version */
+
+/* QEMU-specific boot argument flags */
+#define QEMU_BOOT_FLAG_VERBOSE      (1 << 16)
+#define QEMU_BOOT_FLAG_NO_APPLE     (1 << 17)
+#define QEMU_BOOT_FLAG_GENERIC_ARM  (1 << 18)
+
+/* Function declarations for QEMU compatibility */
+extern void machine_init_qemu_compatibility(void);
+extern void machine_conf_qemu(void);
+extern bool machine_is_qemu_mode(void);
+extern bool machine_apple_silicon_disabled(void);
+
 typedef struct Boot_Video       Boot_Video;
 
 /* Boot argument structure - holds the arguments passed to the kernel at boot time */
-- 
2.34.1
