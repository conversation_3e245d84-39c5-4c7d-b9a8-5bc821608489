/*
 *  GRUB  --  GRand Unified Bootloader
 *  Copyright (C) 2024  Free Software Foundation, Inc.
 *
 *  Apple Silicon Cache Operations for ARM64 XNU loader
 *
 *  GRUB is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 */

#include <grub/types.h>
#include <grub/mm.h>
#include <grub/misc.h>
#include <grub/err.h>
#include <grub/dl.h>
#include <grub/cache.h>

/* Apple Silicon cache line sizes */
#define APPLE_DCACHE_LINE_SIZE  64
#define APPLE_ICACHE_LINE_SIZE  64
#define APPLE_CACHE_ALIGN_MASK  (APPLE_DCACHE_LINE_SIZE - 1)

/* Apple Silicon cache levels */
#define APPLE_L1_CACHE_SIZE     (128 * 1024)   /* 128KB L1 */
#define APPLE_L2_CACHE_SIZE     (12 * 1024 * 1024)  /* 12MB L2 */
#define APPLE_SLC_CACHE_SIZE    (32 * 1024 * 1024)  /* 32MB SLC */

/* Cache operation types */
enum apple_cache_op
{
  APPLE_CACHE_CLEAN,
  APPLE_CACHE_INVALIDATE,
  APPLE_CACHE_CLEAN_INVALIDATE,
  APPLE_CACHE_ZERO
};

/* Cache level definitions */
enum apple_cache_level
{
  APPLE_CACHE_L1D,
  APPLE_CACHE_L1I,
  APPLE_CACHE_L2,
  APPLE_CACHE_SLC,
  APPLE_CACHE_ALL
};

/* Forward declarations */
static void apple_cache_operation_by_va (grub_addr_t start, grub_addr_t end, 
                                        enum apple_cache_op op);
static void apple_cache_operation_by_set_way (enum apple_cache_level level, 
                                             enum apple_cache_op op);
static grub_uint64_t apple_cache_get_line_size (enum apple_cache_level level);
static void apple_cache_barrier (void);

/* Get cache line size for specific cache level */
static grub_uint64_t
apple_cache_get_line_size (enum apple_cache_level level)
{
  grub_uint64_t ctr_el0;
  
  asm volatile ("mrs %0, ctr_el0" : "=r" (ctr_el0));
  
  switch (level)
    {
    case APPLE_CACHE_L1D:
      return 4 << ((ctr_el0 >> 16) & 0xf);  /* DminLine */
    case APPLE_CACHE_L1I:
      return 4 << (ctr_el0 & 0xf);          /* IminLine */
    default:
      return APPLE_DCACHE_LINE_SIZE;
    }
}

/* Memory barrier operations */
static void
apple_cache_barrier (void)
{
  /* Data Synchronization Barrier */
  asm volatile ("dsb sy" ::: "memory");
  
  /* Instruction Synchronization Barrier */
  asm volatile ("isb" ::: "memory");
}

/* Cache operations by virtual address */
static void
apple_cache_operation_by_va (grub_addr_t start, grub_addr_t end, 
                             enum apple_cache_op op)
{
  grub_addr_t addr;
  grub_uint64_t line_size = APPLE_DCACHE_LINE_SIZE;
  
  /* Align start address down to cache line boundary */
  start &= ~APPLE_CACHE_ALIGN_MASK;
  
  /* Align end address up to cache line boundary */
  end = (end + APPLE_CACHE_ALIGN_MASK) & ~APPLE_CACHE_ALIGN_MASK;
  
  for (addr = start; addr < end; addr += line_size)
    {
      switch (op)
        {
        case APPLE_CACHE_CLEAN:
          asm volatile ("dc cvac, %0" : : "r" (addr) : "memory");
          break;
          
        case APPLE_CACHE_INVALIDATE:
          asm volatile ("dc ivac, %0" : : "r" (addr) : "memory");
          break;
          
        case APPLE_CACHE_CLEAN_INVALIDATE:
          asm volatile ("dc civac, %0" : : "r" (addr) : "memory");
          break;
          
        case APPLE_CACHE_ZERO:
          asm volatile ("dc zva, %0" : : "r" (addr) : "memory");
          break;
        }
    }
  
  apple_cache_barrier ();
}

/* Cache operations by set/way */
static void
apple_cache_operation_by_set_way (enum apple_cache_level level, 
                                 enum apple_cache_op op)
{
  grub_uint64_t ccsidr, sets, ways, set, way;
  grub_uint64_t level_shift = (level == APPLE_CACHE_L1D) ? 0 : 2;
  
  /* Select cache level */
  asm volatile ("msr csselr_el1, %0" : : "r" (level_shift));
  asm volatile ("isb");
  
  /* Read cache size ID register */
  asm volatile ("mrs %0, ccsidr_el1" : "=r" (ccsidr));
  
  /* Extract sets and ways */
  sets = ((ccsidr >> 13) & 0x7fff) + 1;
  ways = ((ccsidr >> 3) & 0x3ff) + 1;
  
  for (set = 0; set < sets; set++)
    {
      for (way = 0; way < ways; way++)
        {
          grub_uint64_t val = (way << 30) | (set << 6) | level_shift;
          
          switch (op)
            {
            case APPLE_CACHE_CLEAN:
              asm volatile ("dc csw, %0" : : "r" (val) : "memory");
              break;
              
            case APPLE_CACHE_INVALIDATE:
              asm volatile ("dc isw, %0" : : "r" (val) : "memory");
              break;
              
            case APPLE_CACHE_CLEAN_INVALIDATE:
              asm volatile ("dc cisw, %0" : : "r" (val) : "memory");
              break;
              
            default:
              break;
            }
        }
    }
  
  apple_cache_barrier ();
}

/* Clean data cache range */
void
grub_apple_cache_clean_range (grub_addr_t start, grub_size_t size)
{
  grub_dprintf ("apple_cache", "Cleaning cache range 0x%llx-0x%llx\n",
               start, start + size);
  
  apple_cache_operation_by_va (start, start + size, APPLE_CACHE_CLEAN);
}

/* Invalidate data cache range */
void
grub_apple_cache_invalidate_range (grub_addr_t start, grub_size_t size)
{
  grub_dprintf ("apple_cache", "Invalidating cache range 0x%llx-0x%llx\n",
               start, start + size);
  
  apple_cache_operation_by_va (start, start + size, APPLE_CACHE_INVALIDATE);
}

/* Clean and invalidate data cache range */
void
grub_apple_cache_clean_invalidate_range (grub_addr_t start, grub_size_t size)
{
  grub_dprintf ("apple_cache", "Clean+invalidating cache range 0x%llx-0x%llx\n",
               start, start + size);
  
  apple_cache_operation_by_va (start, start + size, APPLE_CACHE_CLEAN_INVALIDATE);
}

/* Zero cache range (Apple Silicon specific) */
void
grub_apple_cache_zero_range (grub_addr_t start, grub_size_t size)
{
  grub_dprintf ("apple_cache", "Zeroing cache range 0x%llx-0x%llx\n",
               start, start + size);
  
  apple_cache_operation_by_va (start, start + size, APPLE_CACHE_ZERO);
}

/* Invalidate instruction cache */
void
grub_apple_cache_invalidate_icache (void)
{
  grub_dprintf ("apple_cache", "Invalidating instruction cache\n");
  
  /* Invalidate entire instruction cache */
  asm volatile ("ic ialluis" ::: "memory");
  apple_cache_barrier ();
}

/* Invalidate instruction cache range */
void
grub_apple_cache_invalidate_icache_range (grub_addr_t start, grub_size_t size)
{
  grub_addr_t addr;
  grub_uint64_t line_size = apple_cache_get_line_size (APPLE_CACHE_L1I);
  grub_addr_t end = start + size;
  
  grub_dprintf ("apple_cache", "Invalidating icache range 0x%llx-0x%llx\n",
               start, end);
  
  /* Align to instruction cache line boundaries */
  start &= ~(line_size - 1);
  end = (end + line_size - 1) & ~(line_size - 1);
  
  for (addr = start; addr < end; addr += line_size)
    {
      asm volatile ("ic ivau, %0" : : "r" (addr) : "memory");
    }
  
  apple_cache_barrier ();
}

/* Flush all caches (clean and invalidate) */
void
grub_apple_cache_flush_all (void)
{
  grub_dprintf ("apple_cache", "Flushing all caches\n");
  
  /* Clean and invalidate L1 data cache */
  apple_cache_operation_by_set_way (APPLE_CACHE_L1D, APPLE_CACHE_CLEAN_INVALIDATE);
  
  /* Clean and invalidate L2 cache */
  apple_cache_operation_by_set_way (APPLE_CACHE_L2, APPLE_CACHE_CLEAN_INVALIDATE);
  
  /* Invalidate instruction cache */
  grub_apple_cache_invalidate_icache ();
  
  apple_cache_barrier ();
}

/* Disable data cache */
void
grub_apple_cache_disable_dcache (void)
{
  grub_uint64_t sctlr;
  
  grub_dprintf ("apple_cache", "Disabling data cache\n");
  
  /* Clean and invalidate all data caches first */
  apple_cache_operation_by_set_way (APPLE_CACHE_L1D, APPLE_CACHE_CLEAN_INVALIDATE);
  apple_cache_operation_by_set_way (APPLE_CACHE_L2, APPLE_CACHE_CLEAN_INVALIDATE);
  
  /* Disable data cache in SCTLR_EL1 */
  asm volatile ("mrs %0, sctlr_el1" : "=r" (sctlr));
  sctlr &= ~(1 << 2);  /* Clear C bit */
  asm volatile ("msr sctlr_el1, %0" : : "r" (sctlr));
  
  apple_cache_barrier ();
}

/* Disable instruction cache */
void
grub_apple_cache_disable_icache (void)
{
  grub_uint64_t sctlr;
  
  grub_dprintf ("apple_cache", "Disabling instruction cache\n");
  
  /* Invalidate instruction cache first */
  grub_apple_cache_invalidate_icache ();
  
  /* Disable instruction cache in SCTLR_EL1 */
  asm volatile ("mrs %0, sctlr_el1" : "=r" (sctlr));
  sctlr &= ~(1 << 12);  /* Clear I bit */
  asm volatile ("msr sctlr_el1, %0" : : "r" (sctlr));
  
  apple_cache_barrier ();
}

/* Disable all caches */
void
grub_apple_cache_disable_all (void)
{
  grub_dprintf ("apple_cache", "Disabling all caches\n");
  
  grub_apple_cache_disable_dcache ();
  grub_apple_cache_disable_icache ();
}

/* Sync caches for code execution (clean dcache, invalidate icache) */
void
grub_apple_cache_sync_code (grub_addr_t start, grub_size_t size)
{
  grub_dprintf ("apple_cache", "Syncing caches for code at 0x%llx-0x%llx\n",
               start, start + size);
  
  /* Clean data cache to ensure code is written to memory */
  grub_apple_cache_clean_range (start, size);
  
  /* Invalidate instruction cache to ensure fresh fetch */
  grub_apple_cache_invalidate_icache_range (start, size);
}

/* Apple Silicon specific cache initialization */
grub_err_t
grub_apple_cache_init (void)
{
  grub_uint64_t ctr_el0, ccsidr;
  
  grub_dprintf ("apple_cache", "Initializing Apple Silicon cache management\n");
  
  /* Read cache type register */
  asm volatile ("mrs %0, ctr_el0" : "=r" (ctr_el0));
  
  /* Read cache size ID register for L1 data cache */
  asm volatile ("msr csselr_el1, %0" : : "r" (0));  /* Select L1 data cache */
  asm volatile ("isb");
  asm volatile ("mrs %0, ccsidr_el1" : "=r" (ccsidr));
  
  grub_dprintf ("apple_cache", "Cache configuration:\n");
  grub_dprintf ("apple_cache", "  CTR_EL0:    0x%llx\n", ctr_el0);
  grub_dprintf ("apple_cache", "  CCSIDR_EL1: 0x%llx\n", ccsidr);
  grub_dprintf ("apple_cache", "  D-cache line size: %llu bytes\n", 
               apple_cache_get_line_size (APPLE_CACHE_L1D));
  grub_dprintf ("apple_cache", "  I-cache line size: %llu bytes\n", 
               apple_cache_get_line_size (APPLE_CACHE_L1I));
  
  return GRUB_ERR_NONE;
}

/* Get cache information */
void
grub_apple_cache_get_info (grub_uint64_t *dcache_line_size,
                          grub_uint64_t *icache_line_size)
{
  if (dcache_line_size)
    *dcache_line_size = apple_cache_get_line_size (APPLE_CACHE_L1D);
    
  if (icache_line_size)
    *icache_line_size = apple_cache_get_line_size (APPLE_CACHE_L1I);
}
