/*
 *  GRUB  --  GRand Unified Bootloader
 *  Copyright (C) 2024  Free Software Foundation, Inc.
 *
 *  Apple Boot Arguments Structure for ARM64 XNU loader
 *
 *  GRUB is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 */

#include <grub/types.h>
#include <grub/mm.h>
#include <grub/misc.h>
#include <grub/err.h>
#include <grub/dl.h>
#include <grub/efi/efi.h>
#include <grub/video.h>

/* Boot arguments constants */
#define BOOT_LINE_LENGTH            1024
#define BOOT_ARGS_REVISION          2
#define BOOT_ARGS_VERSION           2

/* Video information structure */
struct apple_video_info
{
  grub_uint64_t base_addr;      /* Frame buffer base address */
  grub_uint32_t display;        /* Display ID */
  grub_uint32_t stride;         /* Bytes per row */
  grub_uint32_t width;          /* Width in pixels */
  grub_uint32_t height;         /* Height in pixels */
  grub_uint32_t depth;          /* Bits per pixel */
} GRUB_PACKED;

/* Memory descriptor for Apple Silicon */
struct apple_memory_descriptor
{
  grub_uint64_t base_addr;
  grub_uint64_t size;
  grub_uint32_t type;
  grub_uint32_t flags;
} GRUB_PACKED;

/* Apple Silicon boot arguments structure */
struct apple_boot_args
{
  grub_uint16_t revision;                    /* Boot args revision */
  grub_uint16_t version;                     /* Boot args version */
  
  grub_uint64_t virt_base;                   /* Virtual base address */
  grub_uint64_t phys_base;                   /* Physical base address */
  grub_uint64_t mem_size;                    /* Memory size */
  grub_uint64_t top_of_kernel_data;          /* Top of kernel data */
  
  struct apple_video_info video;             /* Video information */
  
  grub_uint64_t machine_type;                /* Machine type */
  grub_uint64_t devtree;                     /* Device tree address */
  grub_uint32_t devtree_size;                /* Device tree size */
  
  char cmdline[BOOT_LINE_LENGTH];            /* Kernel command line */
  
  /* Apple Silicon specific fields */
  grub_uint64_t boot_flags;                  /* Boot flags */
  grub_uint64_t mem_size_actual;             /* Actual memory size */
  grub_uint64_t kern_args;                   /* Kernel arguments pointer */
  grub_uint64_t kern_entry;                  /* Kernel entry point */
  grub_uint64_t kern_phys_base;              /* Kernel physical base */
  grub_uint64_t kern_phys_slide;             /* Kernel physical slide */
  grub_uint64_t kern_virt_base;              /* Kernel virtual base */
  grub_uint64_t kern_virt_slide;             /* Kernel virtual slide */
  
  /* Memory map information */
  grub_uint32_t mem_map_size;                /* Memory map size */
  grub_uint32_t mem_map_desc_size;           /* Memory descriptor size */
  grub_uint64_t mem_map;                     /* Memory map address */
  
  /* Additional Apple Silicon fields */
  grub_uint64_t firmware_version;            /* Firmware version */
  grub_uint64_t secure_boot_flags;           /* Secure boot flags */
  grub_uint64_t chip_id;                     /* Chip ID */
  grub_uint64_t board_id;                    /* Board ID */
  
} GRUB_PACKED;

/* Boot flags definitions */
#define APPLE_BOOT_FLAG_VERBOSE         (1 << 0)
#define APPLE_BOOT_FLAG_SINGLE_USER     (1 << 1)
#define APPLE_BOOT_FLAG_SAFE_MODE       (1 << 2)
#define APPLE_BOOT_FLAG_DEBUG           (1 << 3)
#define APPLE_BOOT_FLAG_GRAPHICS_MODE   (1 << 4)

/* Forward declarations */
static grub_err_t apple_bootargs_setup_video (struct apple_boot_args *args);
static grub_err_t apple_bootargs_setup_memory_map (struct apple_boot_args *args);
static grub_err_t apple_bootargs_parse_cmdline (struct apple_boot_args *args, 
                                               const char *cmdline);
static grub_uint64_t apple_bootargs_get_chip_info (void);

/* Initialize Apple boot arguments structure */
grub_err_t
grub_apple_bootargs_init (struct apple_boot_args **args,
                         grub_addr_t *args_phys_addr)
{
  struct apple_boot_args *boot_args;
  grub_err_t err;
  
  grub_dprintf ("apple_bootargs", "Initializing Apple boot arguments\n");
  
  /* Allocate boot arguments structure */
  boot_args = grub_malloc (sizeof (*boot_args));
  if (!boot_args)
    return grub_error (GRUB_ERR_OUT_OF_MEMORY, 
                      "Cannot allocate boot arguments structure");
  
  /* Clear the structure */
  grub_memset (boot_args, 0, sizeof (*boot_args));
  
  /* Set basic fields */
  boot_args->revision = BOOT_ARGS_REVISION;
  boot_args->version = BOOT_ARGS_VERSION;
  boot_args->machine_type = 0; /* Apple Silicon */
  
  /* Setup video information */
  err = apple_bootargs_setup_video (boot_args);
  if (err)
    {
      grub_free (boot_args);
      return err;
    }
  
  /* Setup memory map */
  err = apple_bootargs_setup_memory_map (boot_args);
  if (err)
    {
      grub_free (boot_args);
      return err;
    }
  
  /* Get chip information */
  boot_args->chip_id = apple_bootargs_get_chip_info ();
  boot_args->board_id = 0; /* Will be filled from device tree */
  
  /* Set firmware version */
  boot_args->firmware_version = 0x1000; /* Placeholder */
  
  *args = boot_args;
  *args_phys_addr = (grub_addr_t) boot_args; /* Will be relocated later */
  
  grub_dprintf ("apple_bootargs", "Boot arguments initialized at %p\n", boot_args);
  
  return GRUB_ERR_NONE;
}

/* Setup video information */
static grub_err_t
apple_bootargs_setup_video (struct apple_boot_args *args)
{
  struct grub_video_mode_info mode_info;
  grub_err_t err;
  
  grub_dprintf ("apple_bootargs", "Setting up video information\n");
  
  /* Get current video mode */
  err = grub_video_get_info (&mode_info);
  if (err)
    {
      grub_dprintf ("apple_bootargs", "No video mode available, using defaults\n");
      
      /* Set default values */
      args->video.base_addr = 0;
      args->video.display = 0;
      args->video.stride = 0;
      args->video.width = 0;
      args->video.height = 0;
      args->video.depth = 0;
      
      return GRUB_ERR_NONE;
    }
  
  /* Fill video information */
  args->video.base_addr = (grub_uint64_t) mode_info.framebuffer;
  args->video.display = 1; /* Primary display */
  args->video.stride = mode_info.pitch;
  args->video.width = mode_info.width;
  args->video.height = mode_info.height;
  args->video.depth = mode_info.bpp;
  
  grub_dprintf ("apple_bootargs", "Video: %ux%u@%u, fb=0x%llx, stride=%u\n",
               args->video.width, args->video.height, args->video.depth,
               args->video.base_addr, args->video.stride);
  
  return GRUB_ERR_NONE;
}

/* Setup memory map */
static grub_err_t
apple_bootargs_setup_memory_map (struct apple_boot_args *args)
{
  grub_efi_uintn_t map_size = 0;
  grub_efi_uintn_t desc_size = 0;
  grub_efi_uint32_t desc_version = 0;
  grub_efi_uintn_t map_key = 0;
  grub_efi_memory_descriptor_t *memory_map = NULL;
  grub_efi_status_t status;
  
  grub_dprintf ("apple_bootargs", "Setting up memory map\n");
  
  /* Get memory map size */
  status = grub_efi_get_memory_map (&map_size, memory_map, &map_key,
                                   &desc_size, &desc_version);
  
  if (status != GRUB_EFI_BUFFER_TOO_SMALL)
    {
      grub_dprintf ("apple_bootargs", "Failed to get memory map size\n");
      args->mem_map = 0;
      args->mem_map_size = 0;
      args->mem_map_desc_size = 0;
      return GRUB_ERR_NONE;
    }
  
  /* Allocate memory for map */
  memory_map = grub_malloc (map_size);
  if (!memory_map)
    {
      grub_dprintf ("apple_bootargs", "Cannot allocate memory map\n");
      args->mem_map = 0;
      args->mem_map_size = 0;
      args->mem_map_desc_size = 0;
      return GRUB_ERR_NONE;
    }
  
  /* Get actual memory map */
  status = grub_efi_get_memory_map (&map_size, memory_map, &map_key,
                                   &desc_size, &desc_version);
  
  if (status != GRUB_EFI_SUCCESS)
    {
      grub_free (memory_map);
      grub_dprintf ("apple_bootargs", "Failed to get memory map\n");
      args->mem_map = 0;
      args->mem_map_size = 0;
      args->mem_map_desc_size = 0;
      return GRUB_ERR_NONE;
    }
  
  /* Store memory map information */
  args->mem_map = (grub_uint64_t) memory_map;
  args->mem_map_size = map_size;
  args->mem_map_desc_size = desc_size;
  
  grub_dprintf ("apple_bootargs", "Memory map: %u bytes, desc_size=%u\n",
               (unsigned) map_size, (unsigned) desc_size);
  
  return GRUB_ERR_NONE;
}

/* Parse kernel command line and set boot flags */
static grub_err_t
apple_bootargs_parse_cmdline (struct apple_boot_args *args, const char *cmdline)
{
  grub_dprintf ("apple_bootargs", "Parsing command line: %s\n", cmdline);
  
  /* Copy command line */
  grub_strncpy (args->cmdline, cmdline, BOOT_LINE_LENGTH - 1);
  args->cmdline[BOOT_LINE_LENGTH - 1] = '\0';
  
  /* Parse boot flags */
  args->boot_flags = 0;
  
  if (grub_strstr (cmdline, "-v"))
    args->boot_flags |= APPLE_BOOT_FLAG_VERBOSE;
    
  if (grub_strstr (cmdline, "-s"))
    args->boot_flags |= APPLE_BOOT_FLAG_SINGLE_USER;
    
  if (grub_strstr (cmdline, "-x"))
    args->boot_flags |= APPLE_BOOT_FLAG_SAFE_MODE;
    
  if (grub_strstr (cmdline, "debug="))
    args->boot_flags |= APPLE_BOOT_FLAG_DEBUG;
  
  grub_dprintf ("apple_bootargs", "Boot flags: 0x%llx\n", args->boot_flags);
  
  return GRUB_ERR_NONE;
}

/* Get Apple Silicon chip information */
static grub_uint64_t
apple_bootargs_get_chip_info (void)
{
  grub_uint64_t midr;
  
  asm volatile ("mrs %0, midr_el1" : "=r" (midr));
  
  /* Extract part number for Apple chips */
  return (midr >> 4) & 0xfff;
}

/* Setup kernel addresses in boot arguments */
grub_err_t
grub_apple_bootargs_setup_kernel (struct apple_boot_args *args,
                                 grub_addr_t kernel_entry,
                                 grub_addr_t kernel_phys_base,
                                 grub_addr_t kernel_virt_base,
                                 grub_size_t kernel_size)
{
  grub_dprintf ("apple_bootargs", "Setting up kernel addresses\n");
  
  args->kern_entry = kernel_entry;
  args->kern_phys_base = kernel_phys_base;
  args->kern_virt_base = kernel_virt_base;
  args->kern_phys_slide = 0; /* No slide for now */
  args->kern_virt_slide = 0; /* No slide for now */
  
  args->virt_base = kernel_virt_base;
  args->phys_base = kernel_phys_base;
  args->mem_size = kernel_size;
  args->top_of_kernel_data = kernel_phys_base + kernel_size;
  
  grub_dprintf ("apple_bootargs", "Kernel entry: 0x%llx\n", args->kern_entry);
  grub_dprintf ("apple_bootargs", "Kernel phys:  0x%llx\n", args->kern_phys_base);
  grub_dprintf ("apple_bootargs", "Kernel virt:  0x%llx\n", args->kern_virt_base);
  grub_dprintf ("apple_bootargs", "Kernel size:  0x%llx\n", args->mem_size);
  
  return GRUB_ERR_NONE;
}

/* Setup device tree in boot arguments */
grub_err_t
grub_apple_bootargs_setup_devtree (struct apple_boot_args *args,
                                  grub_addr_t devtree_addr,
                                  grub_size_t devtree_size)
{
  grub_dprintf ("apple_bootargs", "Setting up device tree\n");
  
  args->devtree = devtree_addr;
  args->devtree_size = devtree_size;
  
  grub_dprintf ("apple_bootargs", "Device tree: 0x%llx (size: %u)\n",
               args->devtree, args->devtree_size);
  
  return GRUB_ERR_NONE;
}

/* Setup command line in boot arguments */
grub_err_t
grub_apple_bootargs_setup_cmdline (struct apple_boot_args *args,
                                  const char *cmdline)
{
  return apple_bootargs_parse_cmdline (args, cmdline);
}

/* Finalize boot arguments before kernel boot */
grub_err_t
grub_apple_bootargs_finalize (struct apple_boot_args *args)
{
  grub_dprintf ("apple_bootargs", "Finalizing boot arguments\n");
  
  /* Set actual memory size from EFI */
  args->mem_size_actual = args->mem_size; /* For now, same as mem_size */
  
  /* Set secure boot flags */
  args->secure_boot_flags = 0; /* Disabled for now */
  
  grub_dprintf ("apple_bootargs", "Boot arguments finalized\n");
  grub_dprintf ("apple_bootargs", "  Revision: %u\n", args->revision);
  grub_dprintf ("apple_bootargs", "  Version:  %u\n", args->version);
  grub_dprintf ("apple_bootargs", "  Chip ID:  0x%llx\n", args->chip_id);
  grub_dprintf ("apple_bootargs", "  Boot flags: 0x%llx\n", args->boot_flags);
  
  return GRUB_ERR_NONE;
}

/* Free boot arguments structure */
void
grub_apple_bootargs_free (struct apple_boot_args *args)
{
  if (args)
    {
      /* Free memory map if allocated */
      if (args->mem_map)
        grub_free ((void *) args->mem_map);
        
      grub_free (args);
    }
}
