/*
 *  GRUB  --  GRand Unified Bootloader
 *  Copyright (C) 2024  Free Software Foundation, Inc.
 *
 *  GRUB is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  GRUB is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with GRUB.  If not, see <http://www.gnu.org/licenses/>.
 */

#include <grub/loader.h>
#include <grub/file.h>
#include <grub/err.h>
#include <grub/dl.h>
#include <grub/mm.h>
#include <grub/misc.h>
#include <grub/efi/efi.h>
#include <grub/cpu/relocator.h>
#include <grub/command.h>
#include <grub/i18n.h>
#include <grub/macho.h>
#include <grub/xnu.h>
#include <grub/env.h>
#include <grub/lib/cmdline.h>
#include <grub/verify.h>
#include <grub/cache.h>

GRUB_MOD_LICENSE ("GPLv3+");

/* Apple Silicon specific constants */
#define APPLE_ARM64_KERNEL_BASE     0x800000000ULL
#define APPLE_ARM64_KERNEL_SIZE     0x100000000ULL
#define APPLE_ARM64_HEAP_BASE       0x900000000ULL
#define APPLE_ARM64_STACK_SIZE      0x100000
#define APPLE_ARM64_PAGE_SIZE       0x4000
#define BOOT_LINE_LENGTH            1024

/* Apple boot arguments structure for ARM64 */
struct apple_boot_args_arm64
{
  grub_uint16_t revision;
  grub_uint16_t version;
  grub_uint64_t virt_base;
  grub_uint64_t phys_base;
  grub_uint64_t mem_size;
  grub_uint64_t top_of_kernel_data;
  grub_uint64_t video_base;
  grub_uint32_t video_display;
  grub_uint32_t video_stride;
  grub_uint32_t video_width;
  grub_uint32_t video_height;
  grub_uint32_t video_depth;
  grub_uint64_t machine_type;
  grub_uint64_t devtree;
  grub_uint32_t devtree_size;
  char cmdline[BOOT_LINE_LENGTH];
} GRUB_PACKED;

/* ARM64 boot state structure */
struct grub_relocator64_state
{
  grub_uint64_t x0;  /* Boot args pointer */
  grub_uint64_t x1;  /* Reserved */
  grub_uint64_t x2;  /* Reserved */
  grub_uint64_t x3;  /* Reserved */
  grub_uint64_t sp;  /* Stack pointer */
  grub_uint64_t pc;  /* Entry point */
};

static grub_dl_t my_mod;
static grub_addr_t xnu_entry_point;
static grub_addr_t xnu_stack;
static grub_size_t xnu_kernel_size;
static char xnu_cmdline[BOOT_LINE_LENGTH];
static int xnu_darwin_version;

/* Forward declarations */
static grub_err_t grub_xnu_boot_arm64 (void);
static grub_err_t grub_xnu_unload_arm64 (void);
static grub_err_t grub_xnu_setup_apple_device_tree (struct apple_boot_args_arm64 *boot_args);
static grub_err_t grub_xnu_setup_memory_layout_arm64 (void);
static void grub_apple_silicon_cache_operations (void);

/* Cache operations - compatible with both Apple Silicon and QEMU */
static void
grub_arm64_cache_operations (void)
{
  /* Standard ARM64 cache maintenance */
  asm volatile("dsb sy");
  asm volatile("isb");

  /* Invalidate instruction cache */
  asm volatile("ic ialluis");
  asm volatile("dsb sy");
  asm volatile("isb");

  /* Clean data cache to point of coherency */
  asm volatile("dc cisw, %0" : : "r"(0));
  asm volatile("dsb sy");
}

/* Detect if running in QEMU environment */
static bool
grub_detect_qemu_environment (void)
{
  const char *firmware_vendor;
  const char *qemu_mode;

  /* Check for explicit QEMU mode */
  qemu_mode = grub_env_get ("qnu_qemu_mode");
  if (qemu_mode && grub_strcmp (qemu_mode, "1") == 0)
    return true;

  /* Check EFI firmware vendor */
  firmware_vendor = grub_env_get ("efi_firmware_vendor");
  if (firmware_vendor && grub_strstr (firmware_vendor, "EDK II"))
    return true;

  /* Check for QEMU-specific SMBIOS */
  const char *system_vendor = grub_env_get ("smbios_system_vendor");
  if (system_vendor && grub_strstr (system_vendor, "QEMU"))
    return true;

  return false;
}

/* ARM64 memory layout management - supports both Apple Silicon and QEMU */
static grub_err_t
grub_xnu_setup_memory_layout_arm64 (void)
{
  grub_addr_t kernel_base;
  grub_size_t kernel_size;
  bool is_qemu = grub_detect_qemu_environment ();

  if (is_qemu)
    {
      /* QEMU ARM64 virt machine memory layout */
      kernel_base = 0x40000000ULL;  /* QEMU virt machine base */
      kernel_size = 0x10000000ULL;  /* 256MB for QEMU */

      grub_dprintf ("xnu", "Using QEMU ARM64 memory layout\n");
      grub_printf ("XNU: QEMU mode detected - using flexible memory layout\n");
    }
  else
    {
      /* Apple Silicon memory layout */
      kernel_base = APPLE_ARM64_KERNEL_BASE;
      kernel_size = APPLE_ARM64_KERNEL_SIZE;

      grub_dprintf ("xnu", "Using Apple Silicon memory layout\n");
    }

  grub_xnu_heap_target_start = kernel_base;

  /* Try to allocate at specific address first */
  if (!grub_efi_allocate_fixed (kernel_base, kernel_size))
    {
      if (is_qemu)
        {
          /* In QEMU, try flexible allocation */
          grub_efi_physical_address_t addr = kernel_base;
          grub_efi_uintn_t pages = (kernel_size + 0xfff) >> 12;

          if (grub_efi_allocate_pages_real (addr, pages,
                                           GRUB_EFI_ALLOCATE_ANY_PAGES,
                                           GRUB_EFI_LOADER_DATA) == GRUB_EFI_SUCCESS)
            {
              grub_xnu_heap_target_start = addr;
              grub_printf ("XNU: QEMU flexible memory allocation at 0x%llx\n", addr);
              return GRUB_ERR_NONE;
            }
        }

      return grub_error (GRUB_ERR_OUT_OF_MEMORY,
                        "Cannot allocate kernel memory region");
    }

  return GRUB_ERR_NONE;
}

/* Convert GRUB device tree to Apple Device Tree format */
static grub_err_t
grub_convert_fdt_to_adt (void **adt_data, grub_size_t *adt_size)
{
  /* This is a simplified conversion - real implementation would need
     comprehensive FDT to ADT translation */
  
  *adt_size = 0x10000; /* 64KB for basic ADT */
  *adt_data = grub_malloc (*adt_size);
  
  if (!*adt_data)
    return grub_error (GRUB_ERR_OUT_OF_MEMORY, "Cannot allocate ADT memory");
    
  /* Initialize basic ADT structure */
  grub_memset (*adt_data, 0, *adt_size);
  
  /* Add basic ADT header and properties */
  grub_uint32_t *adt_header = (grub_uint32_t *) *adt_data;
  adt_header[0] = grub_cpu_to_le32 (0x41445420); /* "ADT " magic */
  adt_header[1] = grub_cpu_to_le32 (*adt_size);
  
  return GRUB_ERR_NONE;
}

/* Add Apple-specific properties to ADT */
static grub_err_t
grub_adt_add_apple_properties (void *adt_data)
{
  /* Add Apple Silicon specific device tree properties */
  /* This would include CPU configuration, memory layout, etc. */
  
  grub_dprintf ("xnu", "Adding Apple Silicon properties to ADT\n");
  
  /* Placeholder for Apple-specific property addition */
  UNUSED (adt_data);
  
  return GRUB_ERR_NONE;
}

/* Device Tree management - supports both Apple ADT and standard FDT */
static grub_err_t
grub_xnu_setup_device_tree (struct apple_boot_args_arm64 *boot_args)
{
  grub_addr_t dt_target;
  grub_size_t dt_size;
  void *dt_data;
  grub_err_t err;
  bool is_qemu = grub_detect_qemu_environment ();

  if (is_qemu)
    {
      /* Create standard FDT for QEMU */
      grub_dprintf ("xnu", "Creating standard FDT for QEMU\n");

      dt_size = 0x10000; /* 64KB for FDT */
      dt_data = grub_malloc (dt_size);
      if (!dt_data)
        return grub_error (GRUB_ERR_OUT_OF_MEMORY, "Cannot allocate FDT");

      /* Create basic FDT structure */
      grub_memset (dt_data, 0, dt_size);

      /* FDT header */
      grub_uint32_t *fdt_header = (grub_uint32_t *) dt_data;
      fdt_header[0] = grub_cpu_to_be32 (0xd00dfeed); /* FDT_MAGIC */
      fdt_header[1] = grub_cpu_to_be32 (dt_size);    /* totalsize */
      fdt_header[2] = grub_cpu_to_be32 (0x38);       /* off_dt_struct */
      fdt_header[3] = grub_cpu_to_be32 (dt_size - 0x100); /* off_dt_strings */
      fdt_header[4] = grub_cpu_to_be32 (0x28);       /* off_mem_rsvmap */
      fdt_header[5] = grub_cpu_to_be32 (17);         /* version */
      fdt_header[6] = grub_cpu_to_be32 (16);         /* last_comp_version */

      /* Add QEMU-specific device tree nodes */
      /* /chosen node with qemu,firmware property */
      /* /memory node with QEMU memory layout */
      /* /cpus node with standard ARM64 CPUs */

      grub_printf ("XNU: Created standard FDT for QEMU\n");
    }
  else
    {
      /* Convert GRUB device tree to Apple ADT format */
      grub_dprintf ("xnu", "Creating Apple ADT\n");

      err = grub_convert_fdt_to_adt (&dt_data, &dt_size);
      if (err)
        return err;

      /* Add Apple-specific properties */
      err = grub_adt_add_apple_properties (dt_data);
      if (err)
        return err;

      grub_printf ("XNU: Created Apple Device Tree\n");
    }

  /* Allocate memory for device tree */
  err = grub_xnu_heap_malloc (dt_size, &dt_data, &dt_target);
  if (err)
    return err;

  /* Setup device tree in boot args */
  boot_args->devtree = dt_target;
  boot_args->devtree_size = dt_size;

  grub_dprintf ("xnu", "Device tree setup complete at 0x%llx\n", dt_target);

  return GRUB_ERR_NONE;
}

/* Main ARM64 XNU boot function */
static grub_err_t
grub_xnu_boot_arm64 (void)
{
  struct grub_relocator64_state state = {0};
  struct apple_boot_args_arm64 *boot_args;
  grub_addr_t boot_args_target;
  grub_err_t err;
  
  grub_dprintf ("xnu", "Starting ARM64 XNU boot process\n");
  
  /* Setup memory layout */
  err = grub_xnu_setup_memory_layout_arm64 ();
  if (err)
    return err;
  
  /* Allocate boot arguments */
  err = grub_xnu_heap_malloc (sizeof (*boot_args), 
                              (void**)&boot_args, 
                              &boot_args_target);
  if (err)
    return err;
    
  /* Initialize boot arguments */
  grub_memset (boot_args, 0, sizeof (*boot_args));
  boot_args->revision = 2;
  boot_args->version = 2;
  boot_args->virt_base = grub_xnu_heap_target_start;
  boot_args->phys_base = grub_xnu_heap_target_start;
  boot_args->mem_size = grub_xnu_heap_size;

  /* Set machine type based on environment */
  if (grub_detect_qemu_environment ())
    {
      boot_args->machine_type = 0x1000; /* QEMU machine type */
      grub_printf ("XNU: Boot arguments configured for QEMU\n");
    }
  else
    {
      boot_args->machine_type = 0; /* Apple Silicon */
      grub_printf ("XNU: Boot arguments configured for Apple Silicon\n");
    }
  
  /* Copy command line */
  grub_strncpy (boot_args->cmdline, xnu_cmdline, BOOT_LINE_LENGTH - 1);
  boot_args->cmdline[BOOT_LINE_LENGTH - 1] = '\0';
  
  /* Setup device tree (Apple ADT or standard FDT) */
  err = grub_xnu_setup_device_tree (boot_args);
  if (err)
    return err;
    
  /* Configure ARM64 state */
  state.x0 = boot_args_target;  /* Boot args in x0 */
  state.x1 = 0;                 /* Reserved */
  state.x2 = 0;                 /* Reserved */
  state.x3 = 0;                 /* Reserved */
  state.sp = xnu_stack;
  state.pc = xnu_entry_point;
  
  /* Perform cache operations */
  grub_arm64_cache_operations ();
  
  /* Sync caches for kernel */
  grub_arch_sync_caches ((void*)grub_xnu_heap_target_start, 
                         grub_xnu_heap_size);
  
  grub_dprintf ("xnu", "Jumping to XNU kernel at 0x%lx\n", xnu_entry_point);
  
  /* Boot XNU kernel */
  return grub_relocator64_boot (grub_xnu_relocator, state, 0);
}

/* Unload XNU */
static grub_err_t
grub_xnu_unload_arm64 (void)
{
  grub_xnu_unlock ();
  grub_dl_unref (my_mod);
  return GRUB_ERR_NONE;
}

/* Load XNU kernel command */
static grub_err_t
grub_cmd_xnu_kernel_arm64 (grub_command_t cmd __attribute__ ((unused)),
                           int argc, char *args[])
{
  grub_err_t err;
  grub_macho_t macho;
  grub_uint64_t startcode, endcode;
  int i;
  char *ptr;
  void *loadaddr;
  grub_addr_t loadaddr_target;

  if (argc < 1)
    return grub_error (GRUB_ERR_BAD_ARGUMENT, N_("filename expected"));

  grub_xnu_unload ();

  /* Open Mach-O file */
  macho = grub_macho_open (args[0], GRUB_FILE_TYPE_XNU_KERNEL, 0);
  if (!macho)
    return grub_errno;

  /* Get kernel size for ARM64 */
  err = grub_macho_size64 (macho, &startcode, &endcode, GRUB_MACHO_NOBSS, args[0]);
  if (err)
    {
      grub_macho_close (macho);
      grub_xnu_unload ();
      return err;
    }

  /* Mask to 28-bit address space for Apple Silicon */
  startcode &= 0x0fffffff;
  endcode &= 0x0fffffff;

  grub_dprintf ("xnu", "ARM64 kernel: start=0x%lx, end=0x%lx\n",
                (unsigned long) startcode, (unsigned long) endcode);

  /* Setup relocator */
  grub_xnu_relocator = grub_relocator_new ();
  if (!grub_xnu_relocator)
    return grub_errno;

  grub_xnu_heap_target_start = startcode;
  err = grub_xnu_heap_malloc (endcode - startcode, &loadaddr, &loadaddr_target);

  if (err)
    {
      grub_macho_close (macho);
      grub_xnu_unload ();
      return err;
    }

  /* Load kernel */
  err = grub_macho_load64 (macho, args[0], (char *) loadaddr - startcode,
                           GRUB_MACHO_NOBSS, &xnu_darwin_version);
  if (err)
    {
      grub_macho_close (macho);
      grub_xnu_unload ();
      return err;
    }

  /* Get entry point */
  xnu_entry_point = grub_macho_get_entry_point64 (macho, args[0]) & 0x0fffffff;
  if (!xnu_entry_point)
    {
      grub_macho_close (macho);
      grub_xnu_unload ();
      return grub_error (GRUB_ERR_BAD_OS, "couldn't find entry point");
    }

  grub_macho_close (macho);

  /* Align heap */
  err = grub_xnu_align_heap (APPLE_ARM64_PAGE_SIZE);
  if (err)
    {
      grub_xnu_unload ();
      return err;
    }

  /* Setup stack */
  xnu_stack = grub_xnu_heap_target_start + grub_xnu_heap_size + APPLE_ARM64_STACK_SIZE;

  /* Copy parameters to kernel command line */
  ptr = xnu_cmdline;
  for (i = 1; i < argc; i++)
    {
      if (ptr + grub_strlen (args[i]) + 1 >= xnu_cmdline + sizeof (xnu_cmdline))
        break;
      grub_memcpy (ptr, args[i], grub_strlen (args[i]));
      ptr += grub_strlen (args[i]);
      *ptr = ' ';
      ptr++;
    }

  /* Replace last space with null terminator */
  if (ptr != xnu_cmdline)
    *(ptr - 1) = 0;

  /* Verify command line */
  err = grub_verify_string (xnu_cmdline, GRUB_VERIFY_KERNEL_CMDLINE);
  if (err)
    return err;

  /* Set loader */
  grub_loader_set (grub_xnu_boot_arm64, grub_xnu_unload_arm64, 0);

  grub_xnu_lock ();
  grub_xnu_is_64bit = 1;

  return GRUB_ERR_NONE;
}

/* XNU module loading for ARM64 */
static grub_err_t
grub_cmd_xnu_module_arm64 (grub_command_t cmd __attribute__ ((unused)),
                           int argc, char *args[])
{
  grub_file_t file;
  grub_size_t size;
  void *loadto;
  grub_addr_t loadto_target;
  grub_err_t err;

  if (argc < 1)
    return grub_error (GRUB_ERR_BAD_ARGUMENT, N_("filename expected"));

  if (!grub_xnu_heap_size)
    return grub_error (GRUB_ERR_BAD_OS, N_("you need to load the kernel first"));

  file = grub_file_open (args[0], GRUB_FILE_TYPE_XNU_MODULE);
  if (!file)
    return grub_errno;

  size = grub_file_size (file);

  err = grub_xnu_heap_malloc (size, &loadto, &loadto_target);
  if (err)
    {
      grub_file_close (file);
      return err;
    }

  if (grub_file_read (file, loadto, size) != (grub_ssize_t) size)
    {
      grub_file_close (file);
      if (!grub_errno)
        grub_error (GRUB_ERR_BAD_OS, N_("premature end of file %s"), args[0]);
      return grub_errno;
    }

  grub_file_close (file);

  return grub_xnu_register_memory ("Module", 0, loadto_target, size);
}

/* Command registration */
static grub_command_t cmd_kernel, cmd_module;

GRUB_MOD_INIT(xnu_arm64)
{
  cmd_kernel = grub_register_command ("xnu_kernel64", grub_cmd_xnu_kernel_arm64,
                                      0, N_("Load XNU kernel for ARM64."));
  cmd_module = grub_register_command ("xnu_module64", grub_cmd_xnu_module_arm64,
                                      0, N_("Load XNU module for ARM64."));
  my_mod = mod;
}

GRUB_MOD_FINI(xnu_arm64)
{
  grub_unregister_command (cmd_kernel);
  grub_unregister_command (cmd_module);
}
