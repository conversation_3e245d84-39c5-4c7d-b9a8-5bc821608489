# Makefile for ARM64 XNU loader components
# Copyright (C) 2024 Free Software Foundation, Inc.

# ARM64 XNU loader module
module = {
  name = xnu_arm64;
  common = loader/arm64/xnu.c;
  common = loader/arm64/apple_dt.c;
  common = loader/arm64/apple_memory.c;
  common = loader/arm64/apple_boot.c;
  common = loader/arm64/apple_cache.c;
  common = loader/arm64/apple_bootargs.c;
  common = loader/arm64/xnu_kernel.c;
  common = loader/arm64/apple_efi.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DGRUB_ARM64_XNU=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# ARM64 relocator for XNU
module = {
  name = relocator_arm64;
  common = lib/arm64/relocator.c;
  common = lib/arm64/relocator_asm.S;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
};

# Apple Device Tree support
module = {
  name = apple_dt;
  common = loader/arm64/apple_dt.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# Apple Silicon memory management
module = {
  name = apple_memory;
  common = loader/arm64/apple_memory.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# Apple Silicon cache operations
module = {
  name = apple_cache;
  common = loader/arm64/apple_cache.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# Apple boot arguments
module = {
  name = apple_bootargs;
  common = loader/arm64/apple_bootargs.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# XNU kernel loader
module = {
  name = xnu_kernel;
  common = loader/arm64/xnu_kernel.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# Apple EFI extensions
module = {
  name = apple_efi;
  common = loader/arm64/apple_efi.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# ARM64 boot state handler
module = {
  name = apple_boot;
  common = loader/arm64/apple_boot.c;
  
  enable = arm64_efi;
  
  cppflags = '-DGRUB_MACHINE_EFI=1';
  cppflags = '-DAPPLE_SILICON=1';
};

# Dependencies for ARM64 XNU loader
xnu_arm64_DEPENDENCIES = apple_dt apple_memory apple_cache apple_bootargs xnu_kernel apple_efi apple_boot relocator_arm64;

# Compiler flags for Apple Silicon support
ARM64_XNU_CFLAGS = -DGRUB_ARM64_XNU=1 -DAPPLE_SILICON=1 -DGRUB_MACHINE_EFI=1

# Linker flags for ARM64 XNU
ARM64_XNU_LDFLAGS = -Wl,--build-id=none

# Installation rules
install-arm64-xnu: $(xnu_arm64_MODULE)
	$(INSTALL_DATA) $(xnu_arm64_MODULE) $(DESTDIR)$(pkglibdir)/arm64-efi/

# Clean rules
clean-arm64-xnu:
	rm -f $(xnu_arm64_MODULE)
	rm -f $(apple_dt_MODULE)
	rm -f $(apple_memory_MODULE)
	rm -f $(apple_cache_MODULE)
	rm -f $(apple_bootargs_MODULE)
	rm -f $(xnu_kernel_MODULE)
	rm -f $(apple_efi_MODULE)
	rm -f $(apple_boot_MODULE)
	rm -f $(relocator_arm64_MODULE)

.PHONY: install-arm64-xnu clean-arm64-xnu
