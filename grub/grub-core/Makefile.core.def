AutoGen definitions Makefile.tpl;

transform_data = {
  installdir = noinst;
  name = gensyminfo.sh;
  common = gensyminfo.sh.in;
};

transform_data = {
  installdir = noinst;
  name = genmod.sh;
  common = genmod.sh.in;
};

transform_data = {
  installdir = noinst;
  name = modinfo.sh;
  common = modinfo.sh.in;
};

transform_data = {
  installdir = platform;
  name = gdb_helper.py;
  common = gdb_helper.py.in;
};

transform_data = {
  installdir = platform;
  name = gdb_grub;
  common = gdb_grub.in;
};

transform_data = {
  installdir = platform;
  name = grub.chrp;
  common = boot/powerpc/grub.chrp.in;
  enable = powerpc_ieee1275;
};

transform_data = {
  installdir = platform;
  name = bootinfo.txt;
  common = boot/powerpc/bootinfo.txt.in;
  enable = powerpc_ieee1275;
};

kernel = {
  name = kernel;

  nostrip = emu;

  emu_ldflags              = '-Wl,-r';
  i386_efi_cflags          = '-fshort-wchar';
  i386_efi_ldflags         = '-Wl,-r';
  i386_efi_stripflags      = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';
  x86_64_efi_cflags        = '-fshort-wchar';
  x86_64_efi_ldflags       = '-Wl,-r';
  x86_64_efi_stripflags    = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';

  ia64_efi_cflags = '-fshort-wchar -fno-builtin -fpic -minline-int-divide-max-throughput';
  ia64_efi_ldflags = '-Wl,-r';
  ia64_efi_stripflags = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';

  arm_efi_cflags           = '-fshort-wchar';
  arm_efi_ldflags          = '-Wl,-r';
  arm_efi_stripflags       = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';

  arm64_efi_cflags           = '-fshort-wchar';
  arm64_efi_ldflags          = '-Wl,-r';
  arm64_efi_stripflags       = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version -R .eh_frame';

  loongarch64_efi_cflags       = '-fshort-wchar';
  loongarch64_efi_ldflags      = '-Wl,-r';
  loongarch64_efi_stripflags   = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version -R .eh_frame';

  riscv32_efi_cflags       = '-fshort-wchar';
  riscv32_efi_ldflags      = '-Wl,-r';
  riscv32_efi_stripflags   = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version -R .eh_frame';

  riscv64_efi_cflags       = '-fshort-wchar';
  riscv64_efi_ldflags      = '-Wl,-r';
  riscv64_efi_stripflags   = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version -R .eh_frame';

  i386_pc_ldflags          = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags          = '$(TARGET_IMG_BASE_LDOPT),0x9000';
  i386_qemu_ldflags        = '$(TARGET_IMG_LDFLAGS)';
  i386_qemu_ldflags        = '$(TARGET_IMG_BASE_LDOPT),0x9000';
  i386_coreboot_ldflags    = '$(TARGET_IMG_LDFLAGS)';
  i386_coreboot_ldflags    = '$(TARGET_IMG_BASE_LDOPT),0x9000';
  i386_multiboot_ldflags   = '$(TARGET_IMG_LDFLAGS)';
  i386_multiboot_ldflags   = '$(TARGET_IMG_BASE_LDOPT),0x9000';
  i386_ieee1275_ldflags    = '$(TARGET_IMG_LDFLAGS)';
  i386_ieee1275_ldflags    = '$(TARGET_IMG_BASE_LDOPT),0x10000';
  i386_xen_ldflags         = '$(TARGET_IMG_LDFLAGS)';
  i386_xen_ldflags         = '$(TARGET_IMG_BASE_LDOPT),0';
  x86_64_xen_ldflags       = '$(TARGET_IMG_LDFLAGS)';
  x86_64_xen_ldflags       = '$(TARGET_IMG_BASE_LDOPT),0';
  i386_xen_pvh_ldflags     = '$(TARGET_IMG_LDFLAGS)';
  i386_xen_pvh_ldflags     = '$(TARGET_IMG_BASE_LDOPT),0x100000';

  mips_loongson_ldflags    = '-Wl,-Ttext,0x80200000';
  powerpc_ieee1275_ldflags = '-Wl,-Ttext,0x200000';
  sparc64_ieee1275_ldflags = '-Wl,-Ttext,0x4400';
  mips_arc_ldflags    = '-Wl,-Ttext,$(TARGET_LINK_ADDR)';
  mips_qemu_mips_ldflags    = '-Wl,-Ttext,0x80200000';

  mips_arc_cppflags = '-DGRUB_DECOMPRESSOR_LINK_ADDR=$(TARGET_DECOMPRESSOR_LINK_ADDR)';
  i386_qemu_cppflags     = '-DGRUB_BOOT_MACHINE_LINK_ADDR=$(GRUB_BOOT_MACHINE_LINK_ADDR)';
  emu_cflags = '$(CFLAGS_GNULIB)';
  emu_cppflags = '$(CPPFLAGS_GNULIB)';
  arm_uboot_ldflags       = '-Wl,-r';
  arm_uboot_stripflags    = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';
  arm_coreboot_ldflags       = '-Wl,-r';
  arm_coreboot_stripflags    = '--strip-unneeded -K start -R .note -R .comment -R .note.gnu.gold-version';

  i386_pc_startup = kern/i386/pc/startup.S;
  i386_efi_startup = kern/i386/efi/startup.S;
  x86_64_efi_startup = kern/x86_64/efi/startup.S;
  i386_xen_startup = kern/i386/xen/startup.S;
  x86_64_xen_startup = kern/x86_64/xen/startup.S;
  i386_xen_pvh_startup = kern/i386/xen/startup_pvh.S;
  i386_qemu_startup = kern/i386/qemu/startup.S;
  i386_ieee1275_startup = kern/i386/ieee1275/startup.S;
  i386_coreboot_startup = kern/i386/coreboot/startup.S;
  i386_multiboot_startup = kern/i386/coreboot/startup.S;
  mips_startup = kern/mips/startup.S;
  sparc64_ieee1275_startup = kern/sparc64/ieee1275/crt0.S;
  powerpc_ieee1275_startup = kern/powerpc/ieee1275/startup.S;
  arm_uboot_startup = kern/arm/startup.S;
  arm_coreboot_startup = kern/arm/startup.S;
  arm_efi_startup = kern/arm/efi/startup.S;
  arm64_efi_startup = kern/arm64/efi/startup.S;
  loongarch64_efi_startup = kern/loongarch64/efi/startup.S;
  riscv32_efi_startup = kern/riscv/efi/startup.S;
  riscv64_efi_startup = kern/riscv/efi/startup.S;

  common = kern/buffer.c;
  common = kern/command.c;
  common = kern/corecmd.c;
  common = kern/device.c;
  common = kern/disk.c;
  common = kern/dl.c;
  common = kern/env.c;
  common = kern/err.c;
  common = kern/file.c;
  common = kern/fs.c;
  common = kern/list.c;
  common = kern/main.c;
  common = kern/misc.c;
  common = kern/parser.c;
  common = kern/partition.c;
  common = kern/rescue_parser.c;
  common = kern/rescue_reader.c;
  common = kern/term.c;
  common = kern/verifiers.c;

  noemu = kern/compiler-rt.c;
  noemu = kern/mm.c;
  noemu = kern/time.c;
  noemu = kern/generic/millisleep.c;

  noemu_nodist = symlist.c;

  mips = kern/generic/rtc_get_time_ms.c;

  ieee1275 = disk/ieee1275/ofdisk.c;
  ieee1275 = kern/ieee1275/cmain.c;
  ieee1275 = kern/ieee1275/ieee1275.c;
  ieee1275 = kern/ieee1275/mmap.c;
  ieee1275 = kern/ieee1275/openfw.c;
  ieee1275 = term/ieee1275/console.c;
  ieee1275 = kern/ieee1275/init.c;

  uboot = disk/uboot/ubootdisk.c;
  uboot = kern/uboot/uboot.c;
  uboot = kern/uboot/init.c;
  uboot = kern/uboot/hw.c;
  uboot = term/uboot/console.c;
  arm_uboot = kern/arm/uboot/init.c;
  arm_uboot = kern/arm/uboot/uboot.S;

  arm_coreboot = kern/arm/coreboot/init.c;
  arm_coreboot = kern/arm/coreboot/timer.c;
  arm_coreboot = kern/arm/coreboot/coreboot.S;
  arm_coreboot = lib/fdt.c;
  arm_coreboot = bus/fdt.c;
  arm_coreboot = term/ps2.c;
  arm_coreboot = term/arm/pl050.c;
  arm_coreboot = term/arm/cros.c;
  arm_coreboot = term/arm/cros_ec.c;
  arm_coreboot = bus/spi/rk3288_spi.c;
  arm_coreboot = commands/keylayouts.c;
  arm_coreboot = kern/arm/coreboot/dma.c;

  terminfoinkernel = term/terminfo.c;
  terminfoinkernel = term/tparm.c;
  terminfoinkernel = commands/extcmd.c;
  terminfoinkernel = lib/arg.c;

  softdiv = lib/division.c;

  i386 = kern/i386/dl.c;
  i386_xen = kern/i386/dl.c;
  i386_xen_pvh = kern/i386/dl.c;

  i386_coreboot = kern/i386/coreboot/init.c;
  i386_multiboot = kern/i386/coreboot/init.c;
  i386_qemu = kern/i386/qemu/init.c;
  i386_coreboot_multiboot_qemu = term/i386/pc/vga_text.c;
  coreboot = video/coreboot/cbfb.c;

  efi = disk/efi/efidisk.c;
  efi = kern/efi/efi.c;
  efi = kern/efi/debug.c;
  efi = kern/efi/init.c;
  efi = kern/efi/mm.c;
  efi = term/efi/console.c;
  efi = kern/acpi.c;
  efi = kern/efi/acpi.c;
  efi = kern/efi/sb.c;
  efi = kern/lockdown.c;
  i386_coreboot = kern/i386/pc/acpi.c;
  i386_multiboot = kern/i386/pc/acpi.c;
  i386_coreboot = kern/acpi.c;
  i386_multiboot = kern/acpi.c;

  x86 = kern/i386/tsc.c;
  x86 = kern/i386/tsc_pit.c;
  i386_efi = kern/i386/efi/tsc.c;
  x86_64_efi = kern/i386/efi/tsc.c;
  i386_efi = kern/i386/tsc_pmtimer.c;
  i386_coreboot = kern/i386/tsc_pmtimer.c;
  x86_64_efi = kern/i386/tsc_pmtimer.c;

  i386_efi = kern/i386/efi/init.c;
  i386_efi = bus/pci.c;

  x86_64 = kern/x86_64/dl.c;
  x86_64_xen = kern/x86_64/dl.c;
  x86_64_efi = kern/i386/efi/init.c;
  x86_64_efi = bus/pci.c;

  xen = kern/i386/tsc.c;
  xen = kern/i386/xen/tsc.c;
  x86_64_xen = kern/x86_64/xen/hypercall.S;
  i386_xen = kern/i386/xen/hypercall.S;
  xen = kern/xen/init.c;
  xen = term/xen/console.c;
  xen = disk/xen/xendisk.c;
  xen = commands/boot.c;

  i386_xen_pvh = commands/boot.c;
  i386_xen_pvh = disk/xen/xendisk.c;
  i386_xen_pvh = kern/i386/tsc.c;
  i386_xen_pvh = kern/i386/xen/tsc.c;
  i386_xen_pvh = kern/i386/xen/pvh.c;
  i386_xen_pvh = kern/xen/init.c;
  i386_xen_pvh = term/xen/console.c;

  ia64_efi = kern/ia64/efi/startup.S;
  ia64_efi = kern/ia64/efi/init.c;
  ia64_efi = kern/ia64/dl.c;
  ia64_efi = kern/ia64/dl_helper.c;
  ia64_efi = kern/ia64/cache.c;

  arm_efi = kern/arm/efi/init.c;
  arm_efi = kern/efi/fdt.c;

  arm64_efi = kern/arm64/efi/init.c;
  arm64_efi = kern/efi/fdt.c;

  loongarch64_efi = kern/loongarch64/efi/init.c;
  loongarch64_efi = kern/efi/fdt.c;

  riscv32_efi = kern/riscv/efi/init.c;
  riscv32_efi = kern/efi/fdt.c;

  riscv64_efi = kern/riscv/efi/init.c;
  riscv64_efi = kern/efi/fdt.c;

  i386_pc = kern/i386/pc/init.c;
  i386_pc = kern/i386/pc/mmap.c;
  i386_pc = term/i386/pc/console.c;

  i386_qemu = bus/pci.c;
  i386_qemu = kern/vga_init.c;
  i386_qemu = kern/i386/qemu/mmap.c;

  coreboot = kern/coreboot/mmap.c;
  i386_coreboot = kern/i386/coreboot/cbtable.c;
  coreboot = kern/coreboot/cbtable.c;
  arm_coreboot = kern/arm/coreboot/cbtable.c;

  i386_multiboot = kern/i386/multiboot_mmap.c;

  mips = kern/mips/cache.S;
  mips = kern/mips/dl.c;
  mips = kern/mips/init.c;

  mips_qemu_mips = kern/mips/qemu_mips/init.c;
  mips_qemu_mips = term/ns8250.c;
  mips_qemu_mips = term/serial.c;
  mips_qemu_mips = term/at_keyboard.c;
  mips_qemu_mips = term/ps2.c;
  mips_qemu_mips = commands/boot.c;
  mips_qemu_mips = commands/keylayouts.c;
  mips_qemu_mips = term/i386/pc/vga_text.c;
  mips_qemu_mips = kern/vga_init.c;

  mips_arc = kern/mips/arc/init.c;
  mips_arc = term/arc/console.c;
  mips_arc = disk/arc/arcdisk.c;

  mips_loongson = term/ns8250.c;
  mips_loongson = bus/bonito.c;
  mips_loongson = bus/cs5536.c;
  mips_loongson = bus/pci.c;
  mips_loongson = kern/mips/loongson/init.c;
  mips_loongson = term/at_keyboard.c;
  mips_loongson = term/ps2.c;
  mips_loongson = commands/boot.c;
  mips_loongson = term/serial.c;
  mips_loongson = video/sm712.c;
  mips_loongson = video/sis315pro.c;
  mips_loongson = video/radeon_fuloong2e.c;
  mips_loongson = video/radeon_yeeloong3a.c;
  extra_dist = video/sm712_init.c;
  extra_dist = video/sis315_init.c;
  mips_loongson = commands/keylayouts.c;

  powerpc_ieee1275 = kern/powerpc/cache.S;
  powerpc_ieee1275 = kern/powerpc/dl.c;
  powerpc_ieee1275 = kern/powerpc/compiler-rt.S;

  sparc64_ieee1275 = kern/sparc64/cache.S;
  sparc64_ieee1275 = kern/sparc64/dl.c;
  sparc64_ieee1275 = kern/sparc64/ieee1275/ieee1275.c;
  sparc64_ieee1275 = disk/ieee1275/obdisk.c;

  arm = kern/arm/dl.c;
  arm = kern/arm/dl_helper.c;
  arm = kern/arm/cache_armv6.S;
  arm = kern/arm/cache_armv7.S;
  extra_dist = kern/arm/cache.S;
  arm = kern/arm/cache.c;
  arm = kern/arm/compiler-rt.S;

  arm64 = kern/arm64/cache.c;
  arm64 = kern/arm64/cache_flush.S;
  arm64 = kern/arm64/dl.c;
  arm64 = kern/arm64/dl_helper.c;

  loongarch64 = kern/loongarch64/cache.c;
  loongarch64 = kern/loongarch64/cache_flush.S;
  loongarch64 = kern/loongarch64/dl.c;
  loongarch64 = kern/loongarch64/dl_helper.c;

  riscv32 = kern/riscv/cache.c;
  riscv32 = kern/riscv/cache_flush.S;
  riscv32 = kern/riscv/dl.c;

  riscv64 = kern/riscv/cache.c;
  riscv64 = kern/riscv/cache_flush.S;
  riscv64 = kern/riscv/dl.c;

  emu = disk/host.c;
  emu = kern/emu/cache_s.S;
  emu = kern/emu/hostdisk.c;
  emu = osdep/unix/hostdisk.c;
  emu = osdep/exec.c;
  extra_dist = osdep/unix/exec.c;
  emu = osdep/devmapper/hostdisk.c;
  emu = osdep/hostdisk.c;
  emu = kern/emu/hostfs.c;
  emu = kern/emu/main.c;
  emu = kern/emu/argp_common.c;
  emu = kern/emu/misc.c;
  emu = kern/emu/mm.c;
  emu = kern/emu/time.c;
  emu = kern/emu/cache.c;
  emu = osdep/emuconsole.c;
  extra_dist = osdep/unix/emuconsole.c;
  extra_dist = osdep/windows/emuconsole.c;
  emu = osdep/dl.c;
  extra_dist = osdep/unix/dl.c;
  extra_dist = osdep/windows/dl.c;
  emu = osdep/sleep.c;
  emu = osdep/init.c;
  emu = osdep/emunet.c;
  extra_dist = osdep/linux/emunet.c;
  extra_dist = osdep/basic/emunet.c;
  emu = osdep/cputime.c;
  extra_dist = osdep/unix/cputime.c;
  extra_dist = osdep/windows/cputime.c;

  videoinkernel = term/gfxterm.c;
  videoinkernel = font/font.c;
  videoinkernel = font/font_cmd.c;
  videoinkernel = io/bufio.c;
  videoinkernel = video/fb/fbblit.c;
  videoinkernel = video/fb/fbfill.c;
  videoinkernel = video/fb/fbutil.c;
  videoinkernel = video/fb/video_fb.c;
  videoinkernel = video/video.c;

  extra_dist = kern/i386/int.S;
  extra_dist = kern/i386/realmode.S;
  extra_dist = boot/i386/pc/lzma_decode.S;
  extra_dist = kern/mips/cache_flush.S;
};

program = {
  name = grub-emu;
  mansection = 1;

  emu = kern/emu/full.c;
  emu_nodist = grub_emu_init.c;

  ldadd = 'kernel.exec$(EXEEXT)';
  ldadd = '$(MODULE_FILES)';
  ldadd = 'lib/gnulib/libgnu.a $(LIBINTL) $(LIBUTIL) $(LIBSDL) $(SDL2_LIBS) $(LIBUSB) $(LIBPCIACCESS) $(LIBDEVMAPPER) $(LIBZFS) $(LIBNVPAIR) $(LIBGEOM)';

  enable = emu;
};

program = {
  name = grub-emu-lite;

  emu = kern/emu/lite.c;
  emu_nodist = symlist.c;

  ldadd = 'kernel.exec$(EXEEXT)';
  ldadd = 'lib/gnulib/libgnu.a $(LIBINTL) $(LIBUTIL) $(LIBSDL) $(SDL2_LIBS) $(LIBUSB) $(LIBPCIACCESS) $(LIBDEVMAPPER) $(LIBZFS) $(LIBNVPAIR) $(LIBGEOM)';

  enable = emu;
};

image = {
  name = boot;
  i386_pc = boot/i386/pc/boot.S;
  i386_qemu = boot/i386/qemu/boot.S;
  sparc64_ieee1275 = boot/sparc64/ieee1275/boot.S;

  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x7C00';

  i386_qemu_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_qemu_ldflags = '$(TARGET_IMG_BASE_LDOPT),$(GRUB_BOOT_MACHINE_LINK_ADDR)';
  i386_qemu_ccasflags = '-DGRUB_BOOT_MACHINE_LINK_ADDR=$(GRUB_BOOT_MACHINE_LINK_ADDR)';

  /* The entry point for a.out binaries on sparc64 starts
     at 0x4000. Since we are writing the 32 bytes long a.out
     header in the assembly code ourselves, we need to tell
     the linker to adjust the start of the text segment to
     0x4000 - 0x20 = 0x3fe0.
   */
  sparc64_ieee1275_ldflags = ' -Wl,-Ttext=0x3fe0';
  sparc64_ieee1275_objcopyflags = '-O binary';

  objcopyflags = '-O binary';
  enable = i386_pc;
  enable = i386_qemu;
  enable = sparc64_ieee1275;
};

image = {
  name = boot_hybrid;
  i386_pc = boot/i386/pc/boot.S;

  cppflags = '-DHYBRID_BOOT=1';
  
  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x7C00';

  objcopyflags = '-O binary';
  enable = i386_pc;
};

image = {
  name = cdboot;

  i386_pc = boot/i386/pc/cdboot.S;
  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x7C00';

  sparc64_ieee1275 = boot/sparc64/ieee1275/boot.S;

  /* See comment for sparc64_ieee1275_ldflags above. */
  sparc64_ieee1275_ldflags = ' -Wl,-Ttext=0x3fe0';
  sparc64_ieee1275_objcopyflags = '-O binary';
  sparc64_ieee1275_cppflags = '-DCDBOOT=1';

  objcopyflags = '-O binary';

  enable = sparc64_ieee1275;
  enable = i386_pc;
};

image = {
  name = pxeboot;
  i386_pc = boot/i386/pc/pxeboot.S;

  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x7C00';

  objcopyflags = '-O binary';
  enable = i386_pc;
};

image = {
  name = diskboot;
  i386_pc = boot/i386/pc/diskboot.S;

  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x8000';

  sparc64_ieee1275 = boot/sparc64/ieee1275/diskboot.S;
  sparc64_ieee1275_ldflags = '-Wl,-Ttext=0x4200';

  objcopyflags = '-O binary';

  enable = i386_pc;
  enable = sparc64_ieee1275;
};

image = {
  name = lnxboot;
  i386_pc = boot/i386/pc/lnxboot.S;

  i386_pc_ldflags = '$(TARGET_IMG_LDFLAGS)';
  i386_pc_ldflags = '$(TARGET_IMG_BASE_LDOPT),0x6000';

  objcopyflags = '-O binary';
  enable = i386_pc;
};

image = {
  name = xz_decompress;
  mips_head = boot/mips/startup_raw.S;
  common = boot/decompressor/minilib.c;
  common = boot/decompressor/xz.c;
  common = lib/xzembed/xz_dec_bcj.c;
  common = lib/xzembed/xz_dec_lzma2.c;
  common = lib/xzembed/xz_dec_stream.c;
  common = kern/compiler-rt.c;

  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/xzembed -DGRUB_EMBED_DECOMPRESSOR=1';

  objcopyflags = '-O binary';
  mips_ldflags = '-Wl,-Ttext,$(TARGET_DECOMPRESSOR_LINK_ADDR)';
  cflags = '-Wno-unreachable-code';
  enable = mips;
};

image = {
  name = none_decompress;
  mips_head = boot/mips/startup_raw.S;
  common = boot/decompressor/none.c;

  cppflags = '-DGRUB_EMBED_DECOMPRESSOR=1';

  objcopyflags = '-O binary';
  mips_ldflags = '-Wl,-Ttext,$(TARGET_DECOMPRESSOR_LINK_ADDR)';
  enable = mips;
};

image = {
  name = lzma_decompress;
  i386_pc = boot/i386/pc/startup_raw.S;
  i386_pc_nodist = rs_decoder.h;

  objcopyflags = '-O binary';
  ldflags = '$(TARGET_IMG_LDFLAGS) $(TARGET_IMG_BASE_LDOPT),0x8200';
  enable = i386_pc;
};

image = {
  name = fwstart;
  mips_loongson = boot/mips/loongson/fwstart.S;
  objcopyflags = '-O binary';
  ldflags = '-Wl,-N,-S,-Ttext,0xbfc00000,-Bstatic';
  enable = mips_loongson;
};

image = {
  name = fwstart_fuloong2f;
  mips_loongson = boot/mips/loongson/fuloong2f.S;
  objcopyflags = '-O binary';
  ldflags = '-Wl,-N,-S,-Ttext,0xbfc00000,-Bstatic';
  enable = mips_loongson;
};

module = {
  name = disk;
  common = lib/disk.c;
  extra_dist = kern/disk_common.c;
};

module = {
  name = trig;
  common_nodist = trigtables.c;
  extra_dist = gentrigtables.c;
};

module = {
  name = cs5536;
  x86 = bus/cs5536.c;
  enable = x86;
};

module = {
  name = lsspd;
  mips_loongson = commands/mips/loongson/lsspd.c;
  enable = mips_loongson;
};

module = {
  name = usb;
  common = bus/usb/usb.c;
  common = bus/usb/usbtrans.c;
  common = bus/usb/usbhub.c;
  enable = usb;
};

module = {
  name = usbserial_common;
  common = bus/usb/serial/common.c;
  enable = usb;
};

module = {
  name = usbserial_pl2303;
  common = bus/usb/serial/pl2303.c;
  enable = usb;
};

module = {
  name = usbserial_ftdi;
  common = bus/usb/serial/ftdi.c;
  enable = usb;
};

module = {
  name = usbserial_usbdebug;
  common = bus/usb/serial/usbdebug_late.c;
  enable = usb;
};

module = {
  name = uhci;
  common = bus/usb/uhci.c;
  enable = pci;
};

module = {
  name = ohci;
  common = bus/usb/ohci.c;
  enable = pci;
};

module = {
  name = ehci;
  common = bus/usb/ehci.c;
  arm_coreboot = bus/usb/ehci-fdt.c;
  pci = bus/usb/ehci-pci.c;
  enable = pci;
  enable = arm_coreboot;
};

module = {
  name = pci;
  common = bus/pci.c;
  i386_ieee1275 = bus/i386/ieee1275/pci.c;

  enable = i386_pc;
  enable = i386_ieee1275;
  enable = i386_coreboot;
  enable = i386_multiboot;
};

module = {
  name = nativedisk;
  common = commands/nativedisk.c;

  enable = x86;
  enable = mips_loongson;
  enable = mips_qemu_mips;
};

module = {
  name = emupci;
  common = bus/emu/pci.c;
  common = commands/lspci.c;

  enable = emu;
  condition = COND_GRUB_EMU_PCI;
};

module = {
  name = lsdev;
  common = commands/arc/lsdev.c;

  enable = mips_arc;
};

module = {
  name = lsxen;
  common = commands/xen/lsxen.c;

  enable = xen;
};

module = {
  name = cmostest;
  common = commands/i386/cmostest.c;
  enable = cmos;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = cmosdump;
  common = commands/i386/cmosdump.c;
  enable = cmos;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = iorw;
  common = commands/iorw.c;
  enable = x86;
};

module = {
  name = cbtable;
  common = kern/i386/coreboot/cbtable.c;
  common = kern/coreboot/cbtable.c;
  enable = i386_pc;
  enable = i386_efi;
  enable = i386_qemu;
  enable = i386_multiboot;
  enable = i386_ieee1275;
  enable = x86_64_efi;
};

module = {
  name = cbtime;
  common = commands/i386/coreboot/cb_timestamps.c;
  enable = x86;
};

module = {
  name = cbls;
  common = commands/i386/coreboot/cbls.c;
  enable = x86;
};

module = {
  name = cbmemc;
  common = term/i386/coreboot/cbmemc.c;
  enable = x86;
};

module = {
  name = regexp;
  common = commands/regexp.c;
  common = commands/wildcard.c;
  common = lib/gnulib/malloc/dynarray_finalize.c;
  common = lib/gnulib/malloc/dynarray_emplace_enlarge.c;
  common = lib/gnulib/malloc/dynarray_resize.c;
  common = lib/gnulib/regex.c;
  cflags = '$(CFLAGS_POSIX) $(CFLAGS_GNULIB)';
  cppflags = '$(CPPFLAGS_POSIX) $(CPPFLAGS_GNULIB)';
};

module = {
  name = acpi;

  common = commands/acpi.c;
  i386_pc = kern/acpi.c;
  i386_pc = kern/i386/pc/acpi.c;

  enable = efi;
  enable = i386_pc;
  enable = i386_coreboot;
  enable = i386_multiboot;
};

module = {
  name = lsacpi;

  common = commands/lsacpi.c;

  enable = efi;
  enable = i386_pc;
  enable = i386_coreboot;
  enable = i386_multiboot;
};

module = {
  name = lsefisystab;

  common = commands/efi/lsefisystab.c;

  enable = efi;
};

module = {
  name = lssal;

  common = commands/efi/lssal.c;

  enable = efi;
};

module = {
  name = lsefimmap;

  common = commands/efi/lsefimmap.c;

  enable = efi;
};

module = {
  name = lsefi;
  common = commands/efi/lsefi.c;
  enable = efi;
};

module = {
  name = efifwsetup;
  efi = commands/efi/efifwsetup.c;
  enable = efi;
};

module = {
  name = efitextmode;
  efi = commands/efi/efitextmode.c;
  enable = efi;
};

module = {
  name = blocklist;
  common = commands/blocklist.c;
};

module = {
  name = boot;
  common = commands/boot.c;
  i386_pc = lib/i386/pc/biosnum.c;
  enable = x86;
  enable = emu;
  enable = sparc64_ieee1275;
  enable = powerpc_ieee1275;
  enable = mips_arc;
  enable = ia64_efi;
  enable = arm_efi;
  enable = arm64_efi;
  enable = arm_uboot;
  enable = arm_coreboot;
  enable = loongarch64_efi;
  enable = riscv32_efi;
  enable = riscv64_efi;
};

module = {
  name = cat;
  common = commands/cat.c;
};

module = {
  name = cmp;
  common = commands/cmp.c;
};

module = {
  name = configfile;
  common = commands/configfile.c;
};

module = {
  name = cpuid;
  common = commands/i386/cpuid.c;
  enable = x86;
  enable = i386_xen_pvh;
  enable = i386_xen;
  enable = x86_64_xen;
};

module = {
  name = date;
  common = commands/date.c;
};

module = {
  name = drivemap;

  i386_pc = commands/i386/pc/drivemap.c;
  i386_pc = commands/i386/pc/drivemap_int13h.S;
  enable = i386_pc;
};

module = {
  name = echo;
  common = commands/echo.c;
};

module = {
  name = eval;
  common = commands/eval.c;
};

module = {
  name = extcmd;
  common = commands/extcmd.c;
  common = lib/arg.c;
  enable = terminfomodule;
};

module = {
  name = fixvideo;
  common = commands/efi/fixvideo.c;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = gptsync;
  common = commands/gptsync.c;
};

module = {
  name = halt;
  nopc = commands/halt.c;
  i386_pc = commands/i386/pc/halt.c;
  i386_pc = commands/acpihalt.c;
  i386_coreboot = commands/acpihalt.c;
  i386_multiboot = commands/acpihalt.c;
  i386_efi = commands/acpihalt.c;
  x86_64_efi = commands/acpihalt.c;
  i386_multiboot = lib/i386/halt.c;
  i386_coreboot = lib/i386/halt.c;
  i386_qemu = lib/i386/halt.c;
  xen = lib/xen/halt.c;
  i386_xen_pvh = lib/xen/halt.c;
  efi = lib/efi/halt.c;
  ieee1275 = lib/ieee1275/halt.c;
  emu = lib/emu/halt.c;
  uboot = lib/dummy/halt.c;
  arm_coreboot = lib/dummy/halt.c;
};

module = {
  name = reboot;
  i386 = lib/i386/reboot.c;
  i386 = lib/i386/reboot_trampoline.S;
  powerpc_ieee1275 = lib/ieee1275/reboot.c;
  sparc64_ieee1275 = lib/ieee1275/reboot.c;
  mips_arc = lib/mips/arc/reboot.c;
  mips_loongson = lib/mips/loongson/reboot.c;
  mips_qemu_mips = lib/mips/qemu_mips/reboot.c;
  xen = lib/xen/reboot.c;
  i386_xen_pvh = lib/xen/reboot.c;
  uboot = lib/uboot/reboot.c;
  arm_coreboot = lib/dummy/reboot.c;
  common = commands/reboot.c;
};

module = {
  name = hashsum;
  common = commands/hashsum.c;
};

module = {
  name = pgp;
  common = commands/pgp.c;
  cflags = '$(CFLAGS_GCRY) -Wno-redundant-decls';
  cppflags = '$(CPPFLAGS_GCRY)';
};

module = {
  name = hdparm;
  common = commands/hdparm.c;
  enable = pci;
  enable = mips_qemu_mips;
};

module = {
  name = help;
  common = commands/help.c;
};

module = {
  name = hexdump;
  common = commands/hexdump.c;
  common = lib/hexdump.c;
};

module = {
  name = keystatus;
  common = commands/keystatus.c;
};

module = {
  name = loadbios;
  common = commands/efi/loadbios.c;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = loadenv;
  common = commands/loadenv.c;
  common = lib/envblk.c;
};

module = {
  name = ls;
  common = commands/ls.c;
};

module = {
  name = lsmmap;
  common = commands/lsmmap.c;
};

module = {
  name = lspci;
  common = commands/lspci.c;

  enable = pci;
};

module = {
  name = memrw;
  common = commands/memrw.c;
};

module = {
  name = minicmd;
  common = commands/minicmd.c;
};

module = {
  name = parttool;
  common = commands/parttool.c;
};

module = {
  name = password;
  common = commands/password.c;
};

module = {
  name = password_pbkdf2;
  common = commands/password_pbkdf2.c;
};

module = {
  name = play;
  x86 = commands/i386/pc/play.c;
  enable = x86;
};

module = {
  name = spkmodem;
  x86 = term/spkmodem.c;
  enable = x86;
};

module = {
  name = morse;
  x86 = term/morse.c;
  enable = x86;
};

module = {
  name = probe;
  common = commands/probe.c;
};

module = {
  name = read;
  common = commands/read.c;
};

module = {
  name = search;
  common = commands/search_wrap.c;
  extra_dist = commands/search.c;
};

module = {
  name = search_fs_file;
  common = commands/search_file.c;
};

module = {
  name = search_fs_uuid;
  common = commands/search_uuid.c;
};

module = {
  name = search_label;
  common = commands/search_label.c;
};

module = {
  name = setpci;
  common = commands/setpci.c;
  enable = pci;
};

module = {
  name = pcidump;
  common = commands/pcidump.c;
  enable = pci;
};

module = {
  name = sleep;
  common = commands/sleep.c;
};

module = {
  name = smbios;

  common = commands/smbios.c;
  efi = commands/efi/smbios.c;
  i386_pc = commands/i386/pc/smbios.c;
  i386_coreboot = commands/i386/pc/smbios.c;
  i386_multiboot = commands/i386/pc/smbios.c;

  enable = efi;
  enable = i386_pc;
  enable = i386_coreboot;
  enable = i386_multiboot;
};

module = {
  name = suspend;
  ieee1275 = commands/ieee1275/suspend.c;
  enable = i386_ieee1275;
  enable = powerpc_ieee1275;
};

module = {
  name = escc;
  ieee1275 = term/ieee1275/escc.c;
  enable = powerpc_ieee1275;
};

module = {
  name = tpm;
  common = commands/tpm.c;
  ieee1275 = commands/ieee1275/ibmvtpm.c;
  enable = powerpc_ieee1275;
};

module = {
  name = terminal;
  common = commands/terminal.c;
};

module = {
  name = test;
  common = commands/test.c;
};

module = {
  name = true;
  common = commands/true.c;
};

module = {
  name = usbtest;
  common = commands/usbtest.c;
  enable = usb;
};

module = {
  name = videoinfo;
  common = commands/videoinfo.c;
};

module = {
  name = videotest;
  common = commands/videotest.c;
};

module = {
  name = xnu_uuid;
  common = commands/xnu_uuid.c;
};

module = {
  name = dm_nv;
  common = disk/dmraid_nvidia.c;
};

module = {
  name = loopback;
  common = disk/loopback.c;
};

module = {
  name = cryptodisk;
  common = disk/cryptodisk.c;
};

module = {
  name = plainmount;
  common = disk/plainmount.c;
};

module = {
  name = json;
  common = lib/json/json.c;
};

module = {
  name = afsplitter;
  common = disk/AFSplitter.c;
};

module = {
  name = luks;
  common = disk/luks.c;
};

module = {
  name = luks2;
  common = disk/luks2.c;
  common = lib/gnulib/base64.c;
  cflags = '$(CFLAGS_POSIX) $(CFLAGS_GNULIB)';
  cppflags = '$(CPPFLAGS_POSIX) $(CPPFLAGS_GNULIB) -I$(srcdir)/lib/json';
};

module = {
  name = geli;
  common = disk/geli.c;
};

module = {
  name = lvm;
  common = disk/lvm.c;
};

module = {
  name = ldm;
  common = disk/ldm.c;
};

module = {
  name = mdraid09;
  common = disk/mdraid_linux.c;
};

module = {
  name = mdraid09_be;
  common = disk/mdraid_linux_be.c;
};

module = {
  name = mdraid1x;
  common = disk/mdraid1x_linux.c;
};

module = {
  name = diskfilter;
  common = disk/diskfilter.c;
};

module = {
  name = raid5rec;
  common = disk/raid5_recover.c;
};

module = {
  name = raid6rec;
  common = disk/raid6_recover.c;
};

module = {
  name = key_protector;
  common = disk/key_protector.c;
};

module = {
  name = scsi;
  common = disk/scsi.c;
};

module = {
  name = memdisk;
  common = disk/memdisk.c;
};

module = {
  name = ata;
  common = disk/ata.c;
  enable = pci;
  enable = mips_qemu_mips;
};

module = {
  name = ahci;
  common = disk/ahci.c;
  enable = pci;
};

module = {
  name = pata;
  common = disk/pata.c;
  enable = pci;
  enable = mips_qemu_mips;
};

module = {
  name = biosdisk;
  i386_pc = disk/i386/pc/biosdisk.c;
  enable = i386_pc;
};

module = {
  name = usbms;
  common = disk/usbms.c;
  enable = usb;
};

module = {
  name = nand;
  ieee1275 = disk/ieee1275/nand.c;
  enable = i386_ieee1275;
};

module = {
  name = efiemu;
  common = efiemu/main.c;
  common = efiemu/i386/loadcore32.c;
  common = efiemu/i386/loadcore64.c;
  i386_pc = efiemu/i386/pc/cfgtables.c;
  i386_coreboot = efiemu/i386/pc/cfgtables.c;
  i386_multiboot = efiemu/i386/pc/cfgtables.c;
  i386_ieee1275 = efiemu/i386/nocfgtables.c;
  i386_qemu = efiemu/i386/nocfgtables.c;
  common = efiemu/mm.c;
  common = efiemu/loadcore_common.c;
  common = efiemu/symbols.c;
  common = efiemu/loadcore32.c;
  common = efiemu/loadcore64.c;
  common = efiemu/prepare32.c;
  common = efiemu/prepare64.c;
  common = efiemu/pnvram.c;
  common = efiemu/i386/coredetect.c;

  extra_dist = efiemu/prepare.c;
  extra_dist = efiemu/loadcore.c;
  extra_dist = efiemu/runtime/efiemu.S;
  extra_dist = efiemu/runtime/efiemu.c;

  enable = i386_pc;
  enable = i386_coreboot;
  enable = i386_ieee1275;
  enable = i386_multiboot;
  enable = i386_qemu;
};

module = {
  name = font;
  common = font/font.c;
  common = font/font_cmd.c;
  enable = videomodules;
};

module = {
  name = procfs;
  common = fs/proc.c;
};

module = {
  name = affs;
  common = fs/affs.c;
};

module = {
  name = afs;
  common = fs/afs.c;
};

module = {
  name = bfs;
  common = fs/bfs.c;
};

module = {
  name = zstd;
  common = lib/zstd/debug.c;
  common = lib/zstd/entropy_common.c;
  common = lib/zstd/error_private.c;
  common = lib/zstd/fse_decompress.c;
  common = lib/zstd/huf_decompress.c;
  common = lib/zstd/module.c;
  common = lib/zstd/xxhash.c;
  common = lib/zstd/zstd_common.c;
  common = lib/zstd/zstd_decompress.c;
  cflags = '$(CFLAGS_POSIX) -Wno-undef';
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/zstd';
};

module = {
  name = btrfs;
  common = fs/btrfs.c;
  common = lib/crc.c;
  cflags = '$(CFLAGS_POSIX) -Wno-undef';
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/minilzo -I$(srcdir)/lib/zstd -DMINILZO_HAVE_CONFIG_H';
};

module = {
  name = archelp;
  common = fs/archelp.c;
};

module = {
  name = cbfs;
  common = fs/cbfs.c;
};

module = {
  name = cpio;
  common = fs/cpio.c;
};

module = {
  name = cpio_be;
  common = fs/cpio_be.c;
};

module = {
  name = newc;
  common = fs/newc.c;
};

module = {
  name = odc;
  common = fs/odc.c;
};

module = {
  name = erofs;
  common = fs/erofs.c;
};

module = {
  name = ext2;
  common = fs/ext2.c;
};

module = {
  name = fat;
  common = fs/fat.c;
};

module = {
  name = exfat;
  common = fs/exfat.c;
};

module = {
  name = f2fs;
  common = fs/f2fs.c;
};

module = {
  name = fshelp;
  common = fs/fshelp.c;
};

module = {
  name = hfs;
  common = fs/hfs.c;
};

module = {
  name = hfsplus;
  common = fs/hfsplus.c;
};

module = {
  name = hfspluscomp;
  common = fs/hfspluscomp.c;
};

module = {
  name = iso9660;
  common = fs/iso9660.c;
};

module = {
  name = jfs;
  common = fs/jfs.c;
};

module = {
  name = minix;
  common = fs/minix.c;
};

module = {
  name = minix2;
  common = fs/minix2.c;
};

module = {
  name = minix3;
  common = fs/minix3.c;
};

module = {
  name = minix_be;
  common = fs/minix_be.c;
};

module = {
  name = minix2_be;
  common = fs/minix2_be.c;
};

module = {
  name = minix3_be;
  common = fs/minix3_be.c;
};

module = {
  name = nilfs2;
  common = fs/nilfs2.c;
};

module = {
  name = ntfs;
  common = fs/ntfs.c;
};

module = {
  name = ntfscomp;
  common = fs/ntfscomp.c;
};

module = {
  name = reiserfs;
  common = fs/reiserfs.c;
};

module = {
  name = romfs;
  common = fs/romfs.c;
};

module = {
  name = sfs;
  common = fs/sfs.c;
};

module = {
  name = squash4;
  common = fs/squash4.c;
  cflags = '$(CFLAGS_POSIX) -Wno-undef';
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/xzembed -I$(srcdir)/lib/minilzo -DMINILZO_HAVE_CONFIG_H';
};

module = {
  name = tar;
  common = fs/tar.c;
};

module = {
  name = udf;
  common = fs/udf.c;
};

module = {
  name = ufs1;
  common = fs/ufs.c;
};

module = {
  name = ufs1_be;
  common = fs/ufs_be.c;
};

module = {
  name = ufs2;
  common = fs/ufs2.c;
};

module = {
  name = xfs;
  common = fs/xfs.c;
};

module = {
  name = zfs;
  common = fs/zfs/zfs.c;
  common = fs/zfs/zfs_lzjb.c;
  common = fs/zfs/zfs_lz4.c;
  common = fs/zfs/zfs_sha256.c;
  common = fs/zfs/zfs_fletcher.c;
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/zstd';
};

module = {
  name = zfscrypt;
  common = fs/zfs/zfscrypt.c;
};

module = {
  name = zfsinfo;
  common = fs/zfs/zfsinfo.c;
};

module = {
  name = macbless;
  common = commands/macbless.c;
};

module = {
  name = pxe;
  i386_pc = net/drivers/i386/pc/pxe.c;
  enable = i386_pc;
};

module = {
  name = gettext;
  common = gettext/gettext.c;
};

module = {
  name = gfxmenu;
  common = gfxmenu/gfxmenu.c;
  common = gfxmenu/view.c;
  common = gfxmenu/font.c;
  common = gfxmenu/icon_manager.c;
  common = gfxmenu/theme_loader.c;
  common = gfxmenu/widget-box.c;
  common = gfxmenu/gui_canvas.c;
  common = gfxmenu/gui_circular_progress.c;
  common = gfxmenu/gui_box.c;
  common = gfxmenu/gui_label.c;
  common = gfxmenu/gui_list.c;
  common = gfxmenu/gui_image.c;
  common = gfxmenu/gui_progress_bar.c;
  common = gfxmenu/gui_util.c;
  common = gfxmenu/gui_string_util.c;
};

module = {
  name = hello;
  common = hello/hello.c;
};

module = {
  name = gzio;
  common = io/gzio.c;
};

module = {
  name = offsetio;
  common = io/offset.c;
};

module = {
  name = bufio;
  common = io/bufio.c;
  enable = videomodules;
};

module = {
  name = elf;
  common = kern/elf.c;

  extra_dist = kern/elfXX.c;
};

module = {
  name = crypto;
  common = lib/crypto.c;

  extra_dist = lib/libgcrypt-grub/cipher/crypto.lst;
};

module = {
  name = pbkdf2;
  common = lib/pbkdf2.c;
};

module = {
  name = relocator;
  common = lib/relocator.c;
  x86 = lib/i386/relocator16.S;
  x86 = lib/i386/relocator32.S;
  x86 = lib/i386/relocator64.S;
  i386_xen_pvh = lib/i386/relocator16.S;
  i386_xen_pvh = lib/i386/relocator32.S;
  i386_xen_pvh = lib/i386/relocator64.S;
  i386 = lib/i386/relocator_asm.S;
  i386_xen_pvh = lib/i386/relocator_asm.S;
  x86_64 = lib/x86_64/relocator_asm.S;
  i386_xen = lib/i386/relocator_asm.S;
  x86_64_xen = lib/x86_64/relocator_asm.S;
  x86 = lib/i386/relocator.c;
  x86 = lib/i386/relocator_common_c.c;
  i386_xen_pvh = lib/i386/relocator.c;
  i386_xen_pvh = lib/i386/relocator_common_c.c;
  ieee1275 = lib/ieee1275/relocator.c;
  efi = lib/efi/relocator.c;
  mips = lib/mips/relocator_asm.S;
  mips = lib/mips/relocator.c;
  powerpc = lib/powerpc/relocator_asm.S;
  powerpc = lib/powerpc/relocator.c;
  xen = lib/xen/relocator.c;
  i386_xen = lib/i386/xen/relocator.S;
  x86_64_xen = lib/x86_64/xen/relocator.S;
  xen = lib/i386/relocator_common_c.c;
  x86_64_efi = lib/x86_64/efi/relocator.c;

  extra_dist = lib/i386/relocator_common.S;
  extra_dist = kern/powerpc/cache_flush.S;

  enable = mips;
  enable = powerpc;
  enable = x86;
  enable = i386_xen_pvh;
  enable = xen;
};

module = {
  name = datetime;
  common = lib/datetime.c;
  cmos = lib/cmos_datetime.c;
  efi = lib/efi/datetime.c;
  uboot = lib/dummy/datetime.c;
  arm_coreboot = lib/dummy/datetime.c;
  sparc64_ieee1275 = lib/ieee1275/datetime.c;
  powerpc_ieee1275 = lib/ieee1275/datetime.c;
  sparc64_ieee1275 = lib/ieee1275/cmos.c;
  powerpc_ieee1275 = lib/ieee1275/cmos.c;
  xen = lib/xen/datetime.c;
  i386_xen_pvh = lib/xen/datetime.c;

  mips_arc = lib/arc/datetime.c;
};

module = {
  name = setjmp;
  common = lib/setjmp.S;
  extra_dist = lib/i386/setjmp.S;
  extra_dist = lib/mips/setjmp.S;
  extra_dist = lib/x86_64/setjmp.S;
  extra_dist = lib/sparc64/setjmp.S;
  extra_dist = lib/powerpc/setjmp.S;
  extra_dist = lib/ia64/setjmp.S;
  extra_dist = lib/ia64/longjmp.S;
  extra_dist = lib/arm/setjmp.S;
  extra_dist = lib/arm64/setjmp.S;
  extra_dist = lib/riscv/setjmp.S;
  extra_dist = lib/loongarch64/setjmp.S;
};

module = {
  name = aout;
  common = loader/aout.c;
  enable = x86;
};

module = {
  name = bsd;
  x86 = loader/i386/bsd.c;
  x86 = loader/i386/bsd32.c;
  x86 = loader/i386/bsd64.c;

  extra_dist = loader/i386/bsdXX.c;
  extra_dist = loader/i386/bsd_pagetable.c;

  enable = x86;
};

module = {
  name = plan9;
  i386_pc = loader/i386/pc/plan9.c;
  enable = i386_pc;
};


module = {
  name = linux16;
  common = loader/i386/pc/linux.c;
  enable = x86;
};

module = {
  name = ntldr;
  i386_pc = loader/i386/pc/ntldr.c;
  enable = i386_pc;
};


module = {
  name = truecrypt;
  i386_pc = loader/i386/pc/truecrypt.c;
  enable = i386_pc;
};


module = {
  name = freedos;
  i386_pc = loader/i386/pc/freedos.c;
  enable = i386_pc;
};

module = {
  name = pxechain;
  i386_pc = loader/i386/pc/pxechainloader.c;
  enable = i386_pc;
};

module = {
  name = multiboot2;
  cppflags = "-DGRUB_USE_MULTIBOOT2";

  common = loader/multiboot.c;
  common = loader/multiboot_mbi2.c;
  enable = x86;
  enable = i386_xen_pvh;
  enable = mips;
};

module = {
  name = multiboot;
  common = loader/multiboot.c;
  x86 = loader/i386/multiboot_mbi.c;
  i386_xen_pvh = loader/i386/multiboot_mbi.c;
  extra_dist = loader/multiboot_elfxx.c;
  enable = x86;
  enable = i386_xen_pvh;
};

module = {
  name = xen_boot;
  arm64 = loader/arm64/xen_boot.c;
  enable = arm64;
};

module = {
  name = linux;
  x86 = loader/i386/linux.c;
  i386_xen_pvh = loader/i386/linux.c;
  xen = loader/i386/xen.c;
  i386_pc = lib/i386/pc/vesa_modes_table.c;
  i386_xen_pvh = lib/i386/pc/vesa_modes_table.c;
  mips = loader/mips/linux.c;
  powerpc_ieee1275 = loader/powerpc/ieee1275/linux.c;
  sparc64_ieee1275 = loader/sparc64/ieee1275/linux.c;
  ia64_efi = loader/ia64/efi/linux.c;
  arm_coreboot = loader/arm/linux.c;
  arm_efi = loader/efi/linux.c;
  arm_uboot = loader/arm/linux.c;
  arm64 = loader/efi/linux.c;
  loongarch64 = loader/efi/linux.c;
  riscv32 = loader/efi/linux.c;
  riscv64 = loader/efi/linux.c;
  i386_efi = loader/efi/linux.c;
  x86_64_efi = loader/efi/linux.c;
  emu = loader/emu/linux.c;
  common = loader/linux.c;
  common = lib/cmdline.c;
};

module = {
  name = fdt;
  efi = loader/efi/fdt.c;
  common = lib/fdt.c;
  enable = fdt;
};

module = {
  name = xnu;
  x86 = loader/xnu_resume.c;
  x86 = loader/i386/xnu.c;
  x86 = loader/xnu.c;

  /* Code is pretty generic but relies on RNG which
     is available only on few platforms. It's not a
     big deal as xnu needs ACPI anyway and we have
     RNG on all platforms with ACPI.
   */
  enable = i386_multiboot;
  enable = i386_coreboot;
  enable = i386_pc;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = xnu_arm64;
  arm64_efi = loader/arm64/xnu.c;
  arm64_efi = loader/arm64/apple_dt.c;
  arm64_efi = loader/arm64/apple_memory.c;
  arm64_efi = loader/arm64/apple_boot.c;
  arm64_efi = loader/arm64/apple_cache.c;
  arm64_efi = loader/arm64/apple_bootargs.c;
  arm64_efi = loader/arm64/xnu_kernel.c;
  arm64_efi = loader/arm64/apple_efi.c;
  enable = arm64_efi;
  cppflags = '-DGRUB_ARM64_XNU=1';
  cppflags = '-DAPPLE_SILICON=1';
};

module = {
  name = random;
  x86 = lib/i386/random.c;
  common = lib/random.c;

  i386_multiboot = kern/i386/tsc_pmtimer.c;
  i386_coreboot = kern/i386/tsc_pmtimer.c;
  i386_pc = kern/i386/tsc_pmtimer.c;

  enable = i386_multiboot;
  enable = i386_coreboot;
  enable = i386_pc;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = macho;

  common = loader/macho.c;
  common = loader/macho32.c;
  common = loader/macho64.c;
  common = loader/lzss.c;
  extra_dist = loader/machoXX.c;
};

module = {
  name = appleldr;
  common = loader/efi/appleloader.c;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = chain;
  efi = loader/efi/chainloader.c;
  i386_pc = loader/i386/pc/chainloader.c;
  i386_coreboot = loader/i386/coreboot/chainloader.c;
  i386_coreboot = lib/LzmaDec.c;
  enable = i386_pc;
  enable = i386_coreboot;
  enable = efi;
};

module = {
  name = mmap;
  common = mmap/mmap.c;
  x86 = mmap/i386/uppermem.c;
  x86 = mmap/i386/mmap.c;
  i386_xen_pvh = mmap/i386/uppermem.c;
  i386_xen_pvh = mmap/i386/mmap.c;

  i386_pc = mmap/i386/pc/mmap.c;
  i386_pc = mmap/i386/pc/mmap_helper.S;

  efi = mmap/efi/mmap.c;

  mips = mmap/mips/uppermem.c;

  enable = x86;
  enable = i386_xen_pvh;
  enable = ia64_efi;
  enable = arm_efi;
  enable = arm64_efi;
  enable = loongarch64_efi;
  enable = riscv32_efi;
  enable = riscv64_efi;
  enable = mips;
};

module = {
  name = normal;
  common = normal/main.c;
  common = normal/cmdline.c;
  common = normal/dyncmd.c;
  common = normal/auth.c;
  common = normal/autofs.c;
  common = normal/color.c;
  common = normal/completion.c;
  common = normal/menu.c;
  common = normal/menu_entry.c;
  common = normal/menu_text.c;
  common = normal/misc.c;
  common = normal/crypto.c;
  common = normal/term.c;
  common = normal/context.c;
  common = normal/charset.c;
  common = lib/getline.c;

  common = script/main.c;
  common = script/script.c;
  common = script/execute.c;
  common = script/function.c;
  common = script/lexer.c;
  common = script/argv.c;

  common = commands/menuentry.c;

  common = unidata.c;
  common_nodist = grub_script.tab.c;
  common_nodist = grub_script.yy.c;
  common_nodist = grub_script.tab.h;
  common_nodist = grub_script.yy.h;

  extra_dist = script/yylex.l;
  extra_dist = script/parser.y;

  cflags = '$(CFLAGS_POSIX) -Wno-redundant-decls -Wno-unused-but-set-variable';
  cppflags = '$(CPPFLAGS_POSIX)';
};

module = {
  name = part_acorn;
  common = partmap/acorn.c;
};

module = {
  name = part_amiga;
  common = partmap/amiga.c;
};

module = {
  name = part_apple;
  common = partmap/apple.c;
};

module = {
  name = part_gpt;
  common = partmap/gpt.c;
};

module = {
  name = part_msdos;
  common = partmap/msdos.c;
};

module = {
  name = part_sun;
  common = partmap/sun.c;
};

module = {
  name = part_plan;
  common = partmap/plan.c;
};

module = {
  name = part_dvh;
  common = partmap/dvh.c;
};

module = {
  name = part_bsd;
  common = partmap/bsdlabel.c;
};

module = {
  name = part_sunpc;
  common = partmap/sunpc.c;
};

module = {
  name = part_dfly;
  common = partmap/dfly.c;
};

module = {
  name = msdospart;
  common = parttool/msdospart.c;
};

module = {
  name = at_keyboard;
  common = term/at_keyboard.c;
  common = term/ps2.c;
  enable = x86;
};

module = {
  name = gfxterm;
  common = term/gfxterm.c;
  enable = videomodules;
};

module = {
  name = gfxterm_background;
  common = term/gfxterm_background.c;
};

module = {
  name = serial;
  common = term/serial.c;
  x86 = term/ns8250.c;
  x86 = term/ns8250-spcr.c;
  ieee1275 = term/ieee1275/serial.c;
  mips_arc = term/arc/serial.c;
  efi = term/efi/serial.c;
  x86 = term/pci/serial.c;

  enable = terminfomodule;
  enable = ieee1275;
  enable = mips_arc;
};

module = {
  name = sendkey;
  i386_pc = commands/i386/pc/sendkey.c;
  enable = i386_pc;
};

module = {
  name = terminfo;
  common = term/terminfo.c;
  common = term/tparm.c;
  enable = terminfomodule;
};

module = {
  name = usb_keyboard;
  common = term/usb_keyboard.c;
  enable = usb;
};

module = {
  name = vga;
  common = video/i386/pc/vga.c;
  enable = i386_pc;
};

module = {
  name = vga_text;
  common = term/i386/pc/vga_text.c;
  enable = i386_pc;
};

module = {
  name = mda_text;
  common = term/i386/pc/mda_text.c;
  enable = i386_pc;
  enable = i386_coreboot_multiboot_qemu;
};

module = {
  name = video_cirrus;
  x86 = video/cirrus.c;
  enable = x86;
};

module = {
  name = video_bochs;
  x86 = video/bochs.c;
  enable = x86;
};

module = {
  name = functional_test;
  common = tests/lib/functional_test.c;
  common = tests/lib/test.c;
  common = tests/checksums.h;
  common = tests/video_checksum.c;
  common = tests/fake_input.c;
  common = video/capture.c;
};

module = {
  name = exfctest;
  common = tests/example_functional_test.c;
};

module = {
  name = strtoull_test;
  common = tests/strtoull_test.c;
};

module = {
  name = setjmp_test;
  common = tests/setjmp_test.c;
};

module = {
  name = dsa_sexp_test;
  common = tests/dsa_sexp_test.c;

  cflags = '$(CFLAGS_GCRY) -Wno-redundant-decls';
  cppflags = '$(CPPFLAGS_GCRY)';
};

module = {
  name = rsa_sexp_test;
  common = tests/rsa_sexp_test.c;

  cflags = '$(CFLAGS_GCRY) -Wno-redundant-decls';
  cppflags = '$(CPPFLAGS_GCRY)';
};

module = {
  name = signature_test;
  common = tests/signature_test.c;
  common = tests/signatures.h;
};

module = {
  name = sleep_test;
  common = tests/sleep_test.c;
};

module = {
  name = xnu_uuid_test;
  common = tests/xnu_uuid_test.c;
};

module = {
  name = arm64_xnu_test;
  common = tests/arm64_xnu_test.c;
  enable = arm64_efi;
  cppflags = '-DGRUB_ARM64_XNU=1';
  cppflags = '-DAPPLE_SILICON=1';
};

module = {
  name = pbkdf2_test;
  common = tests/pbkdf2_test.c;
};

module = {
  name = legacy_password_test;
  common = tests/legacy_password_test.c;
  enable = i386_pc;
  enable = i386_xen_pvh;
  enable = i386_efi;
  enable = x86_64_efi;
  enable = emu;
  enable = xen;
};

module = {
  name = div;
  common = lib/division.c;
  enable = no_softdiv;
};

module = {
  name = div_test;
  common = tests/div_test.c;
};

module = {
  name = mul_test;
  common = tests/mul_test.c;
};

module = {
  name = shift_test;
  common = tests/shift_test.c;
};

module = {
  name = cmp_test;
  common = tests/cmp_test.c;
};

module = {
  name = ctz_test;
  common = tests/ctz_test.c;
};

module = {
  name = bswap_test;
  common = tests/bswap_test.c;
};

module = {
  name = videotest_checksum;
  common = tests/videotest_checksum.c;
};

/*
 * These tests fail depending on the version of unifont. As we don't distribute
 * our own unifont it fails for most users. Disable them so that they don't mask
 * real failures. They can be reinstated once we solve unifont problem.
module = {
  name = gfxterm_menu;
  common = tests/gfxterm_menu.c;
};

module = {
  name = cmdline_cat_test;
  common = tests/cmdline_cat_test.c;
};
 */

module = {
  name = bitmap;
  common = video/bitmap.c;
};

module = {
  name = bitmap_scale;
  common = video/bitmap_scale.c;
};

module = {
  name = efi_gop;
  efi = video/efi_gop.c;
  enable = efi;
};

module = {
  name = efi_uga;
  efi = video/efi_uga.c;
  enable = i386_efi;
  enable = x86_64_efi;
};

module = {
  name = jpeg;
  common = video/readers/jpeg.c;
};

module = {
  name = png;
  common = video/readers/png.c;
};

module = {
  name = tga;
  common = video/readers/tga.c;
};

module = {
  name = vbe;
  common = video/i386/pc/vbe.c;
  enable = i386_pc;
};

module = {
  name = video_fb;
  common = video/fb/video_fb.c;
  common = video/fb/fbblit.c;
  common = video/fb/fbfill.c;
  common = video/fb/fbutil.c;
  enable = videomodules;
};

module = {
  name = video;
  common = video/video.c;
  enable = videomodules;
};

module = {
  name = video_colors;
  common = video/colors.c;
};

module = {
  name = ieee1275_fb;
  ieee1275 = video/ieee1275.c;
  enable = powerpc_ieee1275;
};

module = {
  name = sdl;
  emu = video/emu/sdl.c;
  enable = emu;
  condition = COND_GRUB_EMU_SDL;
};

module = {
  name = sdl;
  emu = video/emu/sdl.c;
  enable = emu;
  condition = COND_GRUB_EMU_SDL2;
  cflags = '$(SDL2_CFLAGS)';
};

module = {
  name = datehook;
  common = hook/datehook.c;
};

module = {
  name = net;
  common = net/net.c;
  common = net/dns.c;
  common = net/bootp.c;
  common = net/ip.c;
  common = net/udp.c;
  common = net/tcp.c;
  common = net/icmp.c;
  common = net/icmp6.c;
  common = net/ethernet.c;
  common = net/arp.c;
  common = net/netbuff.c;
};

module = {
  name = tftp;
  common = net/tftp.c;
};

module = {
  name = http;
  common = net/http.c;
};

module = {
  name = ofnet;
  common = net/drivers/ieee1275/ofnet.c;
  enable = ieee1275;
};

module = {
  name = ubootnet;
  common = net/drivers/uboot/ubootnet.c;
  enable = uboot;
};

module = {
  name = efinet;
  common = net/drivers/efi/efinet.c;
  enable = efi;
};

module = {
  name = emunet;
  emu = net/drivers/emu/emunet.c;
  enable = emu;
};

module = {
  name = legacycfg;
  common = commands/legacycfg.c;
  common = lib/legacy_parse.c;
  emu = lib/i386/pc/vesa_modes_table.c;
  i386_efi = lib/i386/pc/vesa_modes_table.c;
  x86_64_efi = lib/i386/pc/vesa_modes_table.c;
  xen = lib/i386/pc/vesa_modes_table.c;

  enable = i386_pc;
  enable = i386_xen_pvh;
  enable = i386_efi;
  enable = x86_64_efi;
  enable = emu;
  enable = xen;
};

module = {
  name = syslinuxcfg;
  common = lib/syslinux_parse.c;
  common = commands/syslinuxcfg.c;
};

module = {
  name = test_blockarg;
  common = tests/test_blockarg.c;
};

module = {
  name = xzio;
  common = io/xzio.c;
  common = lib/xzembed/xz_dec_bcj.c;
  common = lib/xzembed/xz_dec_lzma2.c;
  common = lib/xzembed/xz_dec_stream.c;
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/xzembed';
  cflags='-Wno-unreachable-code';
};

module = {
  name = lzopio;
  common = io/lzopio.c;
  common = lib/minilzo/minilzo.c;
  cflags = '$(CFLAGS_POSIX) -Wno-undef -Wno-redundant-decls -Wno-error';
  cppflags = '-I$(srcdir)/lib/posix_wrap -I$(srcdir)/lib/minilzo -DMINILZO_HAVE_CONFIG_H';
};

module = {
  name = testload;
  common = commands/testload.c;
};

module = {
  name = backtrace;
  x86 = lib/i386/backtrace.c;
  i386_xen_pvh = lib/i386/backtrace.c;
  i386_xen = lib/i386/backtrace.c;
  x86_64_xen = lib/i386/backtrace.c;
  common = lib/backtrace.c;
  enable = x86;
  enable = i386_xen_pvh;
  enable = i386_xen;
  enable = x86_64_xen;
};

module = {
  name = lsapm;
  common = commands/i386/pc/lsapm.c;
  enable = i386_pc;
};

module = {
  name = keylayouts;
  common = commands/keylayouts.c;
  enable = x86;
};

module = {
  name = priority_queue;
  common = lib/priority_queue.c;
};

module = {
  name = time;
  common = commands/time.c;
};

module = {
  name = cacheinfo;
  common = commands/cacheinfo.c;
  condition = COND_ENABLE_CACHE_STATS;
};

module = {
  name = boottime;
  common = commands/boottime.c;
  condition = COND_ENABLE_BOOT_TIME_STATS;
};

module = {
  name = adler32;
  common = lib/adler32.c;
};

module = {
  name = crc64;
  common = lib/crc64.c;
};

module = {
  name = mpi;
  common = lib/libgcrypt-grub/mpi/mpiutil.c;
  common = lib/libgcrypt-grub/mpi/mpi-bit.c;
  common = lib/libgcrypt-grub/mpi/mpi-add.c;
  common = lib/libgcrypt-grub/mpi/mpi-mul.c;
  common = lib/libgcrypt-grub/mpi/mpi-mod.c;
  common = lib/libgcrypt-grub/mpi/mpi-gcd.c;
  common = lib/libgcrypt-grub/mpi/mpi-div.c;
  common = lib/libgcrypt-grub/mpi/mpi-cmp.c;
  common = lib/libgcrypt-grub/mpi/mpi-inv.c;
  common = lib/libgcrypt-grub/mpi/mpi-pow.c;
  common = lib/libgcrypt-grub/mpi/mpi-mpow.c;
  common = lib/libgcrypt-grub/mpi/mpih-lshift.c;
  common = lib/libgcrypt-grub/mpi/mpih-mul.c;
  common = lib/libgcrypt-grub/mpi/mpih-mul1.c;
  common = lib/libgcrypt-grub/mpi/mpih-mul2.c;
  common = lib/libgcrypt-grub/mpi/mpih-mul3.c;
  common = lib/libgcrypt-grub/mpi/mpih-add1.c;
  common = lib/libgcrypt-grub/mpi/mpih-sub1.c;
  common = lib/libgcrypt-grub/mpi/mpih-div.c;
  common = lib/libgcrypt-grub/mpi/mpicoder.c;
  common = lib/libgcrypt-grub/mpi/mpih-rshift.c;
  common = lib/libgcrypt-grub/mpi/mpi-inline.c;
  common = lib/libgcrypt-grub/mpi/mpih-const-time.c;
  common = lib/libgcrypt-grub/mpi/mpi-scan.c;
  common = lib/libgcrypt-grub/src/const-time.c;
  common = lib/libgcrypt_wrap/mem.c;
  common = lib/libgcrypt-grub/cipher/md.c;

  cflags = '$(CFLAGS_GCRY) -Wno-redundant-decls -Wno-unused-but-set-variable';
  cppflags = '$(CPPFLAGS_GCRY)';
};

module = {
  name = pubkey;
  common = lib/libgcrypt-grub/cipher/pubkey-util.c;
  common = lib/libgcrypt-grub/cipher/rsa-common.c;
  common = lib/libgcrypt-grub/cipher/dsa-common.c;
  common = lib/libgcrypt-grub/src/sexp.c;
  common = lib/b64dec.c;

  cflags = '$(CFLAGS_GCRY) -Wno-redundant-decls';
  cppflags = '$(CPPFLAGS_GCRY)';
};

module = {
  name = all_video;
  common = lib/fake_module.c;
};

module = {
  name = gdb;
  common = gdb/cstub.c;
  common = gdb/gdb.c;
  i386 = gdb/i386/idt.c;
  i386 = gdb/i386/machdep.S;
  i386 = gdb/i386/signal.c;
  enable = i386;
};

module = {
  name = testspeed;
  common = commands/testspeed.c;
};

module = {
  name = tpm;
  common = commands/tpm.c;
  efi = commands/efi/tpm.c;
  enable = efi;
};

module = {
  name = tss2;
  common = lib/tss2/buffer.c;
  common = lib/tss2/tss2_mu.c;
  common = lib/tss2/tpm2_cmd.c;
  common = lib/tss2/tss2.c;
  efi = lib/efi/tcg2.c;
  emu = lib/tss2/tcg2_emu.c;
  powerpc_ieee1275 = lib/ieee1275/tcg2.c;
  enable = efi;
  enable = emu;
  enable = powerpc_ieee1275;
  cppflags = '-I$(srcdir)/lib/tss2';
};

module = {
  name = tpm2_key_protector;
  common = commands/tpm2_key_protector/args.c;
  common = commands/tpm2_key_protector/module.c;
  common = commands/tpm2_key_protector/tpm2key.c;
  common = commands/tpm2_key_protector/tpm2key_asn1_tab.c;
  /* The plaform support of tpm2_key_protector depends on the tcg2 implementation in tss2. */
  enable = efi;
  enable = emu;
  enable = powerpc_ieee1275;
  cppflags = '-I$(srcdir)/lib/tss2 -I$(srcdir)/lib/libtasn1-grub';
};

module = {
  name = tr;
  common = commands/tr.c;
};

module = {
  name = progress;
  common = lib/progress.c;
};

module = {
  name = file;
  common = commands/file.c;
  common = commands/file32.c;
  common = commands/file64.c;
  extra_dist = commands/fileXX.c;
  common = loader/i386/xen_file.c;
  common = loader/i386/xen_file32.c;
  common = loader/i386/xen_file64.c;
  extra_dist = loader/i386/xen_fileXX.c;
};
module = {
  name = rdmsr;
  common = commands/i386/rdmsr.c;
  enable = x86;
};
module = {
  name = wrmsr;
  common = commands/i386/wrmsr.c;
  enable = x86;
};

module = {
  name = memtools;
  common = commands/memtools.c;
  condition = COND_MM_DEBUG;
};

module = {
  name = bli;
  efi = commands/bli.c;
  enable = efi;
  depends = part_gpt;
};

module = {
  name = asn1;
  common = lib/libtasn1-grub/lib/decoding.c;
  common = lib/libtasn1-grub/lib/coding.c;
  common = lib/libtasn1-grub/lib/element.c;
  common = lib/libtasn1-grub/lib/structure.c;
  common = lib/libtasn1-grub/lib/parser_aux.c;
  common = lib/libtasn1-grub/lib/gstr.c;
  common = lib/libtasn1-grub/lib/errors.c;
  common = lib/libtasn1_wrap/wrap.c;
  cflags = '$(CFLAGS_POSIX) $(CFLAGS_GNULIB)';
  /* -Wno-type-limits comes from configure.ac of libtasn1 */
  cppflags = '$(CPPFLAGS_POSIX) $(CPPFLAGS_GNULIB) -I$(srcdir)/lib/libtasn1-grub -I$(srcdir)/lib/libtasn1-grub/lib -Wno-type-limits';
};

module = {
  name = asn1_test;
  common = tests/asn1/tests/CVE-2018-1000654.c;
  common = tests/asn1/tests/object-id-decoding.c;
  common = tests/asn1/tests/object-id-encoding.c;
  common = tests/asn1/tests/octet-string.c;
  common = tests/asn1/tests/reproducers.c;
  common = tests/asn1/tests/Test_overflow.c;
  common = tests/asn1/tests/Test_simple.c;
  common = tests/asn1/tests/Test_strings.c;
  common = tests/asn1/asn1_test.c;
  cflags = '-Wno-uninitialized';
  cppflags = '-I$(srcdir)/lib/libtasn1-grub -I$(srcdir)/tests/asn1/';
};
