/*
 *  GRUB  --  GRand Unified Bootloader
 *  Copyright (C) 2024  Free Software Foundation, Inc.
 *
 *  ARM64 XNU loader header
 *
 *  GRUB is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 */

#ifndef GRUB_ARM64_XNU_HEADER
#define GRUB_ARM64_XNU_HEADER 1

#include <grub/types.h>
#include <grub/err.h>
#include <grub/file.h>

/* Forward declarations */
struct apple_boot_args;
struct apple_arm64_boot_state;
struct xnu_kernel_info;

/* Apple Device Tree functions */
grub_err_t grub_adt_init (void **adt_data, grub_size_t *adt_size);
grub_err_t grub_convert_fdt_to_adt (void **adt_data, grub_size_t *adt_size);
grub_err_t grub_adt_add_apple_properties (void *adt_data);

/* Apple Silicon Memory Management */
grub_err_t grub_apple_memory_init (void);
grub_err_t grub_apple_memory_fini (void);
grub_err_t grub_apple_memory_alloc_in_region (enum apple_memory_type type,
                                             grub_size_t size,
                                             grub_size_t align,
                                             void **addr,
                                             grub_addr_t *phys_addr);
grub_size_t grub_apple_memory_get_page_size (void);
int grub_apple_memory_is_valid_address (grub_addr_t addr);

/* Apple Silicon Cache Operations */
grub_err_t grub_apple_cache_init (void);
void grub_apple_cache_clean_range (grub_addr_t start, grub_size_t size);
void grub_apple_cache_invalidate_range (grub_addr_t start, grub_size_t size);
void grub_apple_cache_clean_invalidate_range (grub_addr_t start, grub_size_t size);
void grub_apple_cache_zero_range (grub_addr_t start, grub_size_t size);
void grub_apple_cache_invalidate_icache (void);
void grub_apple_cache_invalidate_icache_range (grub_addr_t start, grub_size_t size);
void grub_apple_cache_flush_all (void);
void grub_apple_cache_disable_dcache (void);
void grub_apple_cache_disable_icache (void);
void grub_apple_cache_disable_all (void);
void grub_apple_cache_sync_code (grub_addr_t start, grub_size_t size);
void grub_apple_cache_get_info (grub_uint64_t *dcache_line_size,
                               grub_uint64_t *icache_line_size);

/* Apple Boot Arguments */
grub_err_t grub_apple_bootargs_init (struct apple_boot_args **args,
                                    grub_addr_t *args_phys_addr);
grub_err_t grub_apple_bootargs_setup_kernel (struct apple_boot_args *args,
                                            grub_addr_t kernel_entry,
                                            grub_addr_t kernel_phys_base,
                                            grub_addr_t kernel_virt_base,
                                            grub_size_t kernel_size);
grub_err_t grub_apple_bootargs_setup_devtree (struct apple_boot_args *args,
                                              grub_addr_t devtree_addr,
                                              grub_size_t devtree_size);
grub_err_t grub_apple_bootargs_setup_cmdline (struct apple_boot_args *args,
                                              const char *cmdline);
grub_err_t grub_apple_bootargs_finalize (struct apple_boot_args *args);
void grub_apple_bootargs_free (struct apple_boot_args *args);

/* XNU Kernel Loading */
grub_err_t grub_xnu_kernel_load (grub_file_t file, 
                                struct xnu_kernel_info *info,
                                void **kernel_buffer,
                                grub_addr_t *kernel_phys_addr);
grub_err_t grub_xnu_kernel_verify (struct xnu_kernel_info *info, 
                                  void *kernel_buffer);
grub_err_t grub_xnu_kernel_prepare (struct xnu_kernel_info *info, 
                                   void *kernel_buffer,
                                   grub_addr_t target_addr);
void grub_xnu_kernel_get_info (struct xnu_kernel_info *info,
                              grub_addr_t *entry_point,
                              grub_addr_t *load_addr,
                              grub_size_t *kernel_size);

/* Apple EFI Extensions */
grub_err_t grub_apple_efi_init (void);
grub_err_t grub_apple_efi_set_variable (const char *name, 
                                       const void *data, 
                                       grub_size_t size);
grub_err_t grub_apple_efi_get_variable (const char *name, 
                                       void **data, 
                                       grub_size_t *size);
grub_err_t grub_apple_efi_setup_boot_args (const char *boot_args);
grub_err_t grub_apple_efi_get_system_info (grub_uint64_t *platform_uuid,
                                           grub_uint64_t *system_id);
grub_err_t grub_apple_efi_finalize (void);
void grub_apple_efi_fini (void);

/* ARM64 Boot State Handler */
grub_err_t grub_apple_arm64_boot_state_init (struct apple_arm64_boot_state *state,
                                            grub_addr_t entry_point,
                                            grub_addr_t boot_args,
                                            grub_addr_t stack_pointer);
grub_err_t grub_apple_arm64_prepare_boot (struct apple_arm64_boot_state *state);
grub_err_t grub_apple_arm64_execute_boot (struct apple_arm64_boot_state *state);
grub_uint64_t grub_apple_arm64_get_exception_level (void);
int grub_apple_arm64_is_apple_silicon (void);
grub_uint64_t grub_apple_arm64_get_chip_id (void);
void grub_apple_arm64_print_system_info (void);

/* Memory region types */
enum apple_memory_type
{
  APPLE_MEM_KERNEL,
  APPLE_MEM_HEAP,
  APPLE_MEM_STACK,
  APPLE_MEM_DEVICETREE,
  APPLE_MEM_BOOTARGS,
  APPLE_MEM_RESERVED
};

/* Constants */
#define APPLE_ARM64_KERNEL_BASE     0x800000000ULL
#define APPLE_ARM64_KERNEL_SIZE     0x100000000ULL
#define APPLE_ARM64_HEAP_BASE       0x900000000ULL
#define APPLE_ARM64_STACK_SIZE      0x100000
#define APPLE_ARM64_PAGE_SIZE       0x4000
#define BOOT_LINE_LENGTH            1024

/* Boot flags */
#define APPLE_BOOT_FLAG_VERBOSE         (1 << 0)
#define APPLE_BOOT_FLAG_SINGLE_USER     (1 << 1)
#define APPLE_BOOT_FLAG_SAFE_MODE       (1 << 2)
#define APPLE_BOOT_FLAG_DEBUG           (1 << 3)
#define APPLE_BOOT_FLAG_GRAPHICS_MODE   (1 << 4)

#endif /* GRUB_ARM64_XNU_HEADER */
